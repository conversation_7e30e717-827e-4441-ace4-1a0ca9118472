'use client';
import CustomDatePicker from '@/components/elements/date-picker/date-picker';
import StringInput from '@/components/Input/StringInput';
import { Radio, RadioGroup } from '@/components/ui/radio';
import useSubmitNewFormHook from '@/hooks/forms/useSubmitNewFormHook';
import {
  Box,
  Button,
  Flex,
  HStack,
  Spinner,
  Stack,
  Text,
  Textarea,
} from '@chakra-ui/react';

const CreateForm = ({ formDetails }: { formDetails: any }) => {
  const { formik, isLoading } = useSubmitNewFormHook({ formDetails });

  console.log('formdetais', formDetails);

  return (
    <Box width={'100%'} p={4} h={'100%'}>
      <form
        onSubmit={formik.handleSubmit}
        style={{
          width: '90%',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          margin: '0 auto',
        }}
      >
        {formDetails.questions.map((question: any) => {
          const fieldName = `question_${question.id}`;
          const isTouched = formik.touched[fieldName];
          const errorMessage =
            typeof formik.errors[fieldName] === 'string'
              ? formik.errors[fieldName]
              : '';

          // Textbox or Number Input
          if (question.type === 'Textbox' || question.type === 'Number') {
            return (
              <Box
                key={question.id}
                width="100%"
                mb={8}
                p={4}
                borderColor="gray.100"
                borderRadius="md"
                bg="white"
                boxShadow="sm"
              >
                {/* Question Title - more prominent */}
                {question.title && (
                  <Text fontSize="lg" fontWeight="600" color="gray.700" mb={2}>
                    {question.title}
                  </Text>
                )}

                {/* Description - subtle but readable */}
                {question.description && (
                  <Text
                    fontSize="sm"
                    color="gray.500"
                    mb={3}
                    lineHeight="short"
                  >
                    {question.description}
                  </Text>
                )}

                {/* Actual Question - clear and emphasized */}
                <Flex align="center" mb={3}>
                  <Text fontSize="md" fontWeight="500" color="gray.800">
                    {question.qt}
                  </Text>
                  {question.required && (
                    <Text as="span" color="red.500" ml={1}>
                      *
                    </Text>
                  )}
                </Flex>

                {/* Input field remains the same */}
                <StringInput
                  inputProps={{
                    name: fieldName,
                    type: question.type === 'Textbox' ? 'text' : 'number',
                    value: formik.values[fieldName] || '',
                    onChange: formik.handleChange,
                    onBlur: formik.handleBlur,
                    placeholder: question.placeholder || '',
                  }}
                  fieldProps={{
                    invalid: isTouched && !!errorMessage,
                    errorText: isTouched && errorMessage ? errorMessage : '',
                    required: question.required,
                  }}
                />
              </Box>
            );
          }

          // TextArea Input
          else if (question.type === 'TextArea') {
            return (
              <Box
                mb={9}
                key={question.id}
                width={'100%'}
                p={4}
                borderColor="gray.100"
                borderRadius="md"
                bg="white"
                boxShadow="sm"
              >
                {question.title && (
                  <Text fontSize="lg" fontWeight="600" color="gray.700" mb={2}>
                    {question.title}
                  </Text>
                )}

                {/* Description - subtle but readable */}
                {question.description && (
                  <Text
                    fontSize="sm"
                    color="gray.500"
                    mb={3}
                    lineHeight="short"
                  >
                    {question.description}
                  </Text>
                )}
                <Textarea
                  name={fieldName}
                  value={formik.values[fieldName] || ''}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  placeholder={question.placeholder || ''}
                  minH="120px"
                  resize="vertical"
                />
                {question.description && (
                  <Text fontSize="sm" color="gray.500" mt={2}>
                    {question.description}
                  </Text>
                )}
                {isTouched && errorMessage && (
                  <Text color="red.500" fontSize="sm" mt={1}>
                    {errorMessage}
                  </Text>
                )}
              </Box>
            );
          }

          // Single Choice (Radio Buttons)
          else if (question.type === 'Single choice') {
            return (
              <Box
                mb={9}
                key={question.id}
                width={'100%'}
                p={4}
                borderColor="gray.100"
                borderRadius="md"
                bg="white"
                boxShadow="sm"
              >
                {/* Question Title - more prominent */}
                {question.title && (
                  <Text fontSize="lg" fontWeight="600" color="gray.700" mb={2}>
                    {question.title}
                  </Text>
                )}

                {/* Description - subtle but readable */}
                {question.description && (
                  <Text
                    fontSize="sm"
                    color="gray.500"
                    mb={3}
                    lineHeight="short"
                  >
                    {question.description}
                  </Text>
                )}
                <Text fontWeight="500" mb={2}>
                  {question.qt}
                  {question.required && (
                    <Text as="span" color="red.500">
                      *
                    </Text>
                  )}
                </Text>
                <RadioGroup
                  name={fieldName}
                  onValueChange={(val) =>
                    formik.setFieldValue(fieldName, val.value)
                  }
                  value={formik.values[fieldName] || ''}
                >
                  <Stack direction="column" gap={2}>
                    {question.options.map((option: string, index: number) => (
                      <Radio key={index} value={option}>
                        <Text fontSize="12px">{option}</Text>
                      </Radio>
                    ))}
                  </Stack>
                </RadioGroup>
                {isTouched && errorMessage && (
                  <Text color="red.500" fontSize="sm" mt={1}>
                    {errorMessage}
                  </Text>
                )}
              </Box>
            );
          }

          // Multiple Choice (Checkboxes)
          else if (question.type === 'Multiple choice') {
            return (
              <Box
                mb={9}
                key={question.id}
                width={'100%'}
                textTransform={'capitalize'}
                p={4}
                borderColor="gray.100"
                borderRadius="md"
                bg="white"
                boxShadow="sm"
              >
                {question.title && (
                  <Text fontSize="lg" fontWeight="600" color="gray.700" mb={2}>
                    {question.title}
                  </Text>
                )}

                {/* Description - subtle but readable */}
                {question.description && (
                  <Text
                    fontSize="sm"
                    color="gray.500"
                    mb={3}
                    lineHeight="short"
                  >
                    {question.description}
                  </Text>
                )}
                <Stack direction="column" gap={2}>
                  {question.options.map((option: string, index: number) => (
                    <HStack key={index} align="center">
                      <input
                        type="checkbox"
                        name={fieldName}
                        value={option}
                        checked={
                          formik.values[fieldName]?.includes(option) || false
                        }
                        onChange={(e) => {
                          let newValues = formik.values[fieldName] || [];
                          newValues = e.target.checked
                            ? [...newValues, option]
                            : newValues.filter((val: string) => val !== option);
                          formik.setFieldValue(fieldName, newValues);
                        }}
                        style={{
                          accentColor: 'black',
                          width: '20px',
                          height: '20px',
                          cursor: 'pointer',
                        }}
                      />
                      <Text fontSize="12px" fontWeight={'450'}>
                        {option}
                      </Text>
                    </HStack>
                  ))}
                </Stack>
                {isTouched && errorMessage && (
                  <Text color="red.500" fontSize="sm" mt={1}>
                    {errorMessage}
                  </Text>
                )}
              </Box>
            );
          }

          // Date Picker
          else if (question.type === 'Date') {
            return (
              <Box
                mb={9}
                key={question.id}
                width={'100%'}
                p={4}
                borderColor="gray.100"
                borderRadius="md"
                bg="white"
                boxShadow="sm"
              >
                {question.title && (
                  <Text fontSize="lg" fontWeight="600" color="gray.700" mb={2}>
                    {question.title}
                  </Text>
                )}

                {/* Description - subtle but readable */}
                {question.description && (
                  <Text
                    fontSize="sm"
                    color="gray.500"
                    mb={3}
                    lineHeight="short"
                  >
                    {question.description}
                  </Text>
                )}
                <CustomDatePicker
                  onChange={(e: any) => formik.setFieldValue(fieldName, e)}
                  clearDefault={true}
                />
                {isTouched && errorMessage && (
                  <Text color="red.500" fontSize="sm" mt={1}>
                    {errorMessage}
                  </Text>
                )}
              </Box>
            );
          }

          return null;
        })}

        <Button
          disabled={isLoading}
          type="submit"
          mt={4}
          colorScheme="blue"
          width={'16rem'}
          mb={3}
        >
          {isLoading ? <Spinner /> : 'Submit'}
        </Button>
      </form>
    </Box>
  );
};

export default CreateForm;
