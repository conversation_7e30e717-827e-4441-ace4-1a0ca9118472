import React from 'react';
// import dynamic from 'next/dynamic';
// import { getUserData } from '@/utils/get-user';
// import withAuthorization from '../hoc/Protected';
// import { permissions } from '@/constants/permissions';
import ViewAll from './view-all-consultations/view-all';
import { Metadata } from 'next';
import { cookies } from 'next/headers';
import { Box } from '@chakra-ui/react';

export async function generateMetadata(): Promise<Metadata> {
  const cookieStore = cookies();
  const cookieRaw = cookieStore.get('user_data')?.value;

  let organization_name = 'Soap'; // fallback
  if (cookieRaw) {
    try {
      const parsed = JSON.parse(cookieRaw);
      organization_name = parsed?.organization?.name || 'Soap';
    } catch (err) {
      console.warn('Failed to parse cookie user_data:', err);
    }
  }
  const fullTitle = `Soap - ${organization_name} - Consultations`;
  const description = `Soap Note platform.`;

  return {
    title: fullTitle,
    description,
  };
}

export default async function page() {
  // const userData = getUserData();÷
  // console.log('data is ', userData);

  return (
    <Box>
      <ViewAll />
    </Box>
  );
}
