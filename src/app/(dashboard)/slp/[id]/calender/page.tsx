import { createSupabaseServer } from '@/lib/supabase/server';
import { Metadata } from 'next';
import { cookies } from 'next/headers';
import Calendar from './Calender';

export async function generateMetadata(): Promise<Metadata> {
  const cookieStore = cookies();
  const cookieRaw = cookieStore.get('user_data')?.value;

  let organization_name = 'Soap'; // fallback
  if (cookieRaw) {
    try {
      const parsed = JSON.parse(cookieRaw);
      organization_name = parsed?.organization?.name || 'Soap';
    } catch (err) {
      console.warn('Failed to parse cookie user_data:', err);
    }
  }

  const fullTitle = `Soap - ${organization_name} - Calender`;
  const description = `Soap Note platform.`;

  return {
    title: fullTitle,
    description,
  };
}

export default async function page({ params }: { params: { id: string } }) {
  const supabase = await createSupabaseServer();
  const { data } = await supabase.rpc('get_user_by_id', {
    id_param: Number(params.id),
  });

  // console.log('user in the slp id page is ', data);
  // if (!data) {
  //   return <UnAuthorized />;
  // }

  return (
    <div>
      <Calendar slp={data} />
    </div>
  );
}
