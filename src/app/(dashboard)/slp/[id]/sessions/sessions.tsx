'use client';
import CustomDatePicker from '@/components/elements/date-picker/date-picker';
import EmptyState from '@/components/elements/EmptyState';
import AnimateLoader from '@/components/elements/loader/animate-loader';
import CustomTable from '@/components/table/CustomTable';
import { Button } from '@/components/ui/button';
import { useTemplateHook } from '@/hooks/admin/template/useTemplateHook';
import { IUser } from '@/shared/interface/user';
import {
  Box,
  Center,
  Flex,
  Heading,
  Stack,
  Text,
  useBreakpointValue,
} from '@chakra-ui/react';
import { type Placement } from '@floating-ui/react';
import Link from 'next/link';
import { IconInput } from '../dashboard/Dashboard';
import AddNewSession from './AddNewSession';
import { createColumnDef } from './columnDef';
import { useSlpSession } from './useSlpSession';

import { FiCalendar } from 'react-icons/fi';

export default function Sessions({ slp }: { slp: IUser }) {
  const slpSessionHook = useSlpSession(slp?.id as any, slp);
  const templateHook = useTemplateHook({ slp, size: 1000 });

  const raw = localStorage.getItem('UserState');
  const dataOrg = raw ? JSON.parse(raw) : null;
  const org = dataOrg?.UserState?.organization;

  const placement = useBreakpointValue({
    base: 'bottom-end',
    lg: 'right',
  }) as Placement;

  const {
    handleDateChange,
    SlpSessions,
    SlpSessionsLoading,
    setCurrentYear,
    abbr,
    organizationIdFromParams,
    selectDate,
    highlightDates,
  } = slpSessionHook;

  if (SlpSessionsLoading) {
    return (
      <Box mt={'10rem'}>
        <AnimateLoader />
      </Box>
    );
  }

  const getSessionLink = (id: any) => {
    let link = `/slp/${id}/calender`;
    if (organizationIdFromParams) {
      link += `?organization_id=${organizationIdFromParams}`;
    }
    return link;
  };

  console.log('organizationIdFromParams', organizationIdFromParams);

  return (
    <Box pt={{ base: '3', lg: '5' }} pb={'20'}>
      <Flex
        flexDirection={{ base: 'column', md: 'row' }}
        justifyContent={'space-between'}
        alignItems={{ lg: 'center' }}
        gap={'3'}
      >
        <Box>
          <Heading
            fontSize={{ base: '1.3rem', md: '2rem' }}
            fontWeight={'semibold'}
            mb={{ base: '1', lg: '3' }}
          >
            {slp?.first_name} {slp?.last_name} Sessions
          </Heading>
          <Text
            fontSize={{ base: 'sm', md: 'md' }}
            color={'gray.300'}
            fontWeight={'500'}
          >
            Manage therapy sessions and documentation
          </Text>
        </Box>

        <Flex gap={'1rem'} alignItems={'center'} flexWrap={'wrap'}>
          <Link href={getSessionLink(slp?.id)}>
            <Button
              variant={'outline'}
              px={{ base: '2', md: '4' }}
              rounded={'md'}
              fontWeight={'600'}
              fontSize={{ base: 'sm', md: 'md' }}
              gap={{ base: '2', md: '3' }}
              _hover={{ color: 'white' }}
              borderColor={'primary.500'}
              // leftIcon={<HiDownload />}
              color="primary.500"
            >
              <FiCalendar /> Calendar
            </Button>
          </Link>
          <AddNewSession abbr={abbr} templateHook={templateHook} />
        </Flex>
      </Flex>
      <Box mt={'7'}>
        <Flex alignItems={'center'} justifyContent={'space-between'}>
          <Box>
            <CustomDatePicker
              placement={placement}
              onChange={handleDateChange}
              defaultDate={selectDate}
              timeZone={abbr?.zone}
              highlightDates={highlightDates}
              onMonthYearChange={(year) => setCurrentYear(year)}
              customInput={
                <IconInput
                  selectDate={selectDate}
                  handleDateChange={handleDateChange}
                />
              }
            />
          </Box>
        </Flex>
        {org?.id === 1 && (
          <Stack mt={'7'} bg={'primary.500'} p={'.5'} pl={'1.5'}>
            <Box bg={'primary.50'} px={{ base: '3', md: '5' }} py={'4.5'}>
              <Text fontSize={{ base: 'sm', md: '1.2rem' }} fontWeight={600}>
                Invoice Requests and SOAP Notes
              </Text>
              <Text fontSize={{ base: 'xs', md: '0.9rem' }}>
                After submitting the invoice request, it will take up to 1
                business day for the invoice to be created. SOAP note can be
                modified at any time.
              </Text>
            </Box>
          </Stack>
        )}
      </Box>

      {/* Table
       */}
      <Box minH="20rem" mt="2rem" overflowX="auto" whiteSpace="nowrap">
        {SlpSessionsLoading ? (
          <Center h="20rem">
            <AnimateLoader />
          </Center>
        ) : SlpSessions && SlpSessions.length > 0 ? (
          <CustomTable
            columnDef={createColumnDef(
              abbr,
              slp.id,
              org?.id,
              organizationIdFromParams
            )}
            data={SlpSessions || []}
            NoDataText="No sessions today"
          />
        ) : (
          <EmptyState text="No sessions" minH="fit" />
        )}
      </Box>
    </Box>
  );
}
