import { FullClient } from '@/shared/interface/clients';
import { IBookings } from '@/shared/interface/consultation';
import { ISlpNotes } from '@/shared/interface/slp_notes';
import { Box, Center } from '@chakra-ui/react';
import { createColumnHelper } from '@tanstack/react-table';
import 'moment-timezone';
import 'moment/locale/en-ca';
import moment from 'moment/moment';
import Link from 'next/link';
// import RowAction from './RowAction';
// import CreateSoapNoteModal from './CreateSoapNoteModal';
import { IInvoices } from '@/shared/interface/invoice';
import { formatWithTimeZone } from '@/utils/timezone-format';
// import CreateSoapNoteModal from './CreateSoapNoteModal';

import Status from '@/components/elements/status/Status';
// import { FiEye } from 'react-icons/fi';

export interface SlpSessionDisplay extends IBookings {
  clients: FullClient;
  slp_notes: ISlpNotes & { invoice: IInvoices };
}
const columnHelper = createColumnHelper<SlpSessionDisplay>();
export const createColumnDef = (
  abbr: any,
  slpId: any,
  orgId: any,
  organizationIdFromParams: any
) => {
  const columnDef = [
    columnHelper.display({
      cell: (props) => {
        return (
          <Link href={'/contacts/' + props.row.original?.client_id}>
            <Box color={'rgb(79 70 229)'} fontWeight={500}>
              {`${props.row.original?.clients?.first_name || props.row.original?.first_name || ''} ${
                props.row.original?.clients?.last_name ||
                props.row.original?.last_name ||
                ''
              }`}
            </Box>
          </Link>
        );
      },
      header: 'Name',
      id: 'name',
    }),
    columnHelper.accessor('clients.phone', {
      cell: (info) => <Box>{info.getValue()?.substring(0, 20)}</Box>,
      header: 'Phone',
      id: 'phone',
    }),

    columnHelper.accessor('appointment', {
      cell: (info) => (
        <Box>{`${formatWithTimeZone(info.getValue(), abbr?.zone)}`}</Box>
      ),
      header: `Appointment (${abbr?.abbr})`,
      id: 'appointment-est',
    }),
    columnHelper.accessor('event', {
      cell: (info) => <Box>{info.getValue()}</Box>,
      header: 'Type',
      id: 'type',
    }),

    columnHelper.display({
      id: 'previous_memo',
      header: 'Previous Memo',
      cell: (props) => {
        const currentSessionDate = moment(
          props.row.original?.appointment
        ).format('YYYY-MM-DD');
        const invoices = props.row.original?.clients?.invoices;

        // Find the last memo from a session not on the current date
        const previousInvoice = invoices
          ?.filter(
            (invoice) =>
              moment(invoice?.invoice_date).format('YYYY-MM-DD') !==
              currentSessionDate
          )
          ?.sort(
            (a, b) =>
              Number(new Date(b.invoice_date)) -
              Number(new Date(a.invoice_date))
          )[0];

        return <Box>{previousInvoice?.memo || 'N/A'}</Box>;
      },
    }),
    columnHelper.display({
      id: 'status',
      header: 'Status',
      cell: (props) => {
        const initialBooking = props.row.original;
        console.log('invoice', initialBooking);
        return (
          <Box>
            {
              <Status
                name={initialBooking?.slp_notes?.invoice?.status ?? 'pending'}
              />
            }
          </Box>
        );
      },
    }),
    ...(orgId === 1
      ? [
          columnHelper.display({
            id: 'actions',
            cell: (props) => {
              const initialBooking = props.row.original;
              const bgColor = initialBooking?.slp_notes?.invoice_id
                ? 'green.50'
                : initialBooking?.slp_notes
                  ? 'yellow.50'
                  : '';
              const textColor = initialBooking?.slp_notes?.invoice_id
                ? 'green.700'
                : initialBooking?.slp_notes
                  ? 'yellow.700'
                  : 'rgb(79 70 229)';

              let url = `${slpId}/create-invoice/${props.row.original.id}`;

              // Add organizationId as query param if it exists
              if (organizationIdFromParams) {
                url += `?organization_id=${organizationIdFromParams}`;
              }

              return (
                <Link href={url}>
                  <Box>
                    <Center
                      px={2}
                      py={1}
                      fontWeight="medium"
                      rounded="md"
                      bg={bgColor}
                      color={textColor}
                      maxW={'7.5rem'}
                      cursor={'pointer'}
                    >
                      {initialBooking?.slp_notes?.invoice_id
                        ? 'Created'
                        : initialBooking?.slp_notes
                          ? 'Pending'
                          : 'Create Invoice'}
                    </Center>
                  </Box>
                </Link>
              );
            },
          }),
        ]
      : []),
    //   cell: (props) => (
    //     <Box>
    //       <CreateSoapNoteModal initialBooking={props.row?.original} />
    //     </Box>
    //   ),
    //   header: 'Status',
    // }),
    columnHelper.display({
      id: 'see_details',
      header: 'View',
      cell: (props) => {
        return (
          <Link href={`${slpId}/create-invoice/${props.row.original.id}`}>
            <Box
              // border={'gray 2px solid'}
              display={'flex'}
              justifyContent={'center'}
              color={'blue'}
            >
              See Detail
            </Box>
          </Link>
        );
      },
    }),
  ];

  return columnDef;
};
