import React from 'react';
import { ViewAll } from './view-all';
import { createSupabaseServer } from '@/lib/supabase/server';
import { getUserByEmail } from '@/app/service/user';
import { Center, Heading, Span, Stack, Text } from '@chakra-ui/react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { Metadata } from 'next';
import { cookies } from 'next/headers';

export async function generateMetadata(): Promise<Metadata> {
  const cookieStore = cookies();
  const cookieRaw = cookieStore.get('user_data')?.value;

  let organization_name = 'Soap'; // fallback
  if (cookieRaw) {
    try {
      const parsed = JSON.parse(cookieRaw);
      organization_name = parsed?.organization?.name || 'Soap';
    } catch (err) {
      console.warn('Failed to parse cookie user_data:', err);
    }
  }

  const fullTitle = `Soap - ${organization_name} - Inbox`;
  const description = `Soap Note platform.`;

  return {
    title: fullTitle,
    description,
  };
}

export default async function page() {
  const { auth } = createSupabaseServer();
  const user = await auth.getUser();
  const userFromServer = await getUserByEmail(
    user?.data?.user?.email as string
  );

  if (!userFromServer?.is_gmail_connected) {
    return (
      <Center h={'20rem'} w={'full'}>
        <Stack gap={'4'} w={'1/2'}>
          <Heading textAlign={'center'}>Get sent messages</Heading>

          <Text color={'gray.200'} textAlign={'center'}>
            <Span>Step 1:</Span> Connect your google account so that the
            software can read your gmail.
          </Text>
          <Center>
            <Button bg={'primary.500'} w={'fit-content'}>
              <Link href={`/profile?tab=account-settings`}>
                Connect to gmail Integration
              </Link>
            </Button>
          </Center>
        </Stack>
      </Center>
    );
  }
  return <ViewAll />;
}
