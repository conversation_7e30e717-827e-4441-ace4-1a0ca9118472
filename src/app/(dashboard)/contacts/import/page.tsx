import React from 'react';
import ImportContacts from './ImportContacts';
import { Metadata } from 'next';
import { cookies } from 'next/headers';

export async function generateMetadata(): Promise<Metadata> {
  const cookieStore = cookies();
  const cookieRaw = cookieStore.get('user_data')?.value;

  let organization_name = 'Soap'; // fallback
  if (cookieRaw) {
    try {
      const parsed = JSON.parse(cookieRaw);
      organization_name = parsed?.organization?.name || 'Soap';
    } catch (err) {
      console.warn('Failed to parse cookie user_data:', err);
    }
  }

  const fullTitle = `Soap - ${organization_name} - Contact - Import`;
  const description = `Soap Note platform.`;

  return {
    title: fullTitle,
    description,
  };
}

export default function page() {
  return (
    <div>
      <ImportContacts />
    </div>
  );
}
