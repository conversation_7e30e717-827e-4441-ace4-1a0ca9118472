import dynamic from 'next/dynamic';
import { Metadata } from 'next';
import { cookies } from 'next/headers';

const AllContacts = dynamic(() => import('./AllContacts'), {
  ssr: false,
});

export async function generateMetadata(): Promise<Metadata> {
  const cookieStore = cookies();
  const cookieData: any = cookieStore.get('user_data')?.value;
  const organization_name =
    JSON.parse(cookieData)?.organization?.name || 'Soap';

  const fullTitle = `SOAP - ${organization_name} - Contacts`;
  const description = `Soap Note platform.`;

  return {
    title: fullTitle,
    description,
  };
}

export default function page() {
  return (
    <div>
      <AllContacts />
    </div>
  );
}
