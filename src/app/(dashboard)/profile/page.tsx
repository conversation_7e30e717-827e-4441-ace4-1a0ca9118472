// import General from './General';
import { getUserByEmail } from '@/app/service/user';
import { createSupabaseServer } from '@/lib/supabase/server';
import { Metadata } from 'next';
import dynamic from 'next/dynamic';
import { cookies } from 'next/headers';

const General = dynamic(() => import('./General'), {
  ssr: false,
});

export async function generateMetadata(): Promise<Metadata> {
  const cookieStore = cookies();
  const cookieRaw = cookieStore.get('user_data')?.value;

  let organization_name = 'Soap'; // fallback
  if (cookieRaw) {
    try {
      const parsed = JSON.parse(cookieRaw);
      organization_name = parsed?.organization?.name || 'Soap';
    } catch (err) {
      console.warn('Failed to parse cookie user_data:', err);
    }
  }

  const fullTitle = `Soap - ${organization_name} - Profile`;
  const description = `Soap Note platform.`;

  return {
    title: fullTitle,
    description,
  };
}

export default async function page() {
  const { auth } = createSupabaseServer();
  const user = await auth.getUser();
  const userFromServer = await getUserByEmail(
    user?.data?.user?.email as string
  );

  return (
    <div>
      <General user={userFromServer} />
    </div>
  );
}
