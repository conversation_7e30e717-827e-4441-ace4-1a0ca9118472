import { useUpdatePurchaseMutation } from '@/api/newsf/queries';
import { ConsentDialog } from '@/components/elements/dialog/ConsentDialog';
import { CustomModal } from '@/components/elements/modal/custom-modal';
import {
  MenuContent,
  MenuItem,
  MenuRoot,
  MenuTrigger,
} from '@/components/ui/menu';
import { toaster } from '@/components/ui/toaster';
// import { queryKey } from '@/constants/query-key';
import { ToastMessages } from '@/constants/toast-messages';
import { MenuSeparator, useDisclosure } from '@chakra-ui/react';
import { useQueryClient } from '@tanstack/react-query';
import { BsThreeDotsVertical } from 'react-icons/bs';
import LinkBooking from './LinkBooking';

export default function RowAction({ purchase }: { purchase: any }) {
  const queryClient = useQueryClient();
  const statusDisclosure = useDisclosure();
  const redeemDisclosure = useDisclosure();
  const { mutateAsync: UpdatePurchase, isLoading: UpdatePurchasePending } =
    useUpdatePurchaseMutation();
  const isRedeemed = purchase?.status?.toLowerCase() === 'redeemed';

  const handleStatusUpdate = async () => {
    await UpdatePurchase({
      id: purchase.id,
      payload: {
        status: isRedeemed ? 'UNREDEEMED' : 'REDEEMED',
      },
    });
    toaster.create({
      description: ToastMessages.operationSuccess,
      type: 'success',
    });
    statusDisclosure.onClose();
    await queryClient.invalidateQueries({
      queryKey: ['newsf-get-invoice-by-id', purchase.invoice_id],
    });
    await queryClient.invalidateQueries({
      queryKey: ['newsf-update-purchase'],
    });
  };
  console.log('purchase is ', purchase);

  return (
    <div data-no-row-click="true">
      <MenuRoot positioning={{ placement: 'bottom' }}>
        <MenuTrigger
          border={'none !important'}
          boxShadow={'none !important'}
          cursor={'pointer'}
        >
          <BsThreeDotsVertical />
        </MenuTrigger>
        {purchase?.status !== 'DELETED' && (
          <MenuContent cursor={'pointer'}>
            <MenuItem value="update-status" onClick={redeemDisclosure.onOpen}>
              {isRedeemed ? 'Unredeem' : 'Redeem'}
            </MenuItem>
            <MenuSeparator />
            <MenuItem value="update-manually" onClick={statusDisclosure.onOpen}>
              Manually {isRedeemed ? ' Unredeem' : 'redeem'}
            </MenuItem>
          </MenuContent>
        )}
      </MenuRoot>

      <CustomModal
        open={redeemDisclosure.open}
        onOpenChange={redeemDisclosure.onClose}
      >
        <LinkBooking purchase={purchase} />
      </CustomModal>

      <ConsentDialog
        handleSubmit={handleStatusUpdate}
        open={statusDisclosure.open}
        onOpenChange={statusDisclosure.onClose}
        heading={isRedeemed ? 'Unredeem Purchase' : 'Redeem Purchase?'}
        note={`This will mark this purchase as ${isRedeemed ? 'Unredeemed' : 'Redeemed'}`}
        isLoading={UpdatePurchasePending}
      />
    </div>
  );
}
