import React, { useState } from 'react';
import { Box, Stack, Tabs, Text } from '@chakra-ui/react';
import { Button } from '@/components/ui/button';
import CustomSelect from '@/components/Input/CustomSelect';
import StringInput from '@/components/Input/StringInput';
import { useFormik } from 'formik';
import * as yup from 'yup';
import moment from 'moment';

// Validation schema for new booking
const newBookingSchema = yup.object().shape({
  client_id: yup.string().required('Client selection is required'),
  service_id: yup.string().required('Service selection is required'),
  appointment_date: yup
    .string()
    .test('valid-date', 'Invalid date format', (value) =>
      moment(value, 'YYYY-MM-DDTHH:mm', true).isValid()
    )
    .required('Appointment date is required'),
  duration: yup
    .number()
    .min(15, 'Duration must be at least 15 minutes')
    .required('Duration is required'),
  notes: yup.string().max(500, 'Notes cannot exceed 500 characters').optional(),
});

interface LinkBookingProps {
  selectedClient?: any;
  onBookingCreated?: (booking: any) => void;
  onBookingSelected?: (booking: any) => void;
}

const LinkBooking: React.FC<LinkBookingProps> = ({
  selectedClient,
  onBookingCreated,
  onBookingSelected,
}) => {
  const [activeTab, setActiveTab] = useState('newBooking');

  // Form for creating new booking
  const {
    values,
    handleBlur,
    handleChange,
    setFieldValue,
    errors,
    touched,
    handleSubmit,
  } = useFormik({
    initialValues: {
      client_id: selectedClient?.id || '',
      service_id: '',
      appointment_date: '',
      duration: 60,
      notes: '',
    },
    validationSchema: newBookingSchema,
    onSubmit: async (values) => {
      console.log('Creating new booking with values:', values);
      // TODO: Implement booking creation logic
      if (onBookingCreated) {
        onBookingCreated(values);
      }
    },
  });

  const handleValueChange = (details: { value: string }) => {
    setActiveTab(details.value);
  };

  return (
    <>
      <Text textAlign={'center'} mb={4}>
        Link a booking to this invoice
      </Text>
      <Tabs.Root
        border={'none'}
        defaultValue={'newBooking'}
        lazyMount
        size={{ base: 'sm', md: 'md' }}
        value={activeTab}
        onValueChange={handleValueChange}
      >
        <Box
          style={{ position: 'sticky', zIndex: 1, top: '80px' }}
          bg={'white'}
          borderBottom={{ lg: '1px solid' }}
          borderColor={{ lg: 'gray.50' }}
        >
          <Tabs.List
            display={'flex'}
            border={'none'}
            alignItems={'center'}
            gap={'6'}
            overflowY={'hidden'}
          >
            <Tabs.Trigger
              value={'newBooking'}
              textTransform={'capitalize'}
              _selected={{ color: 'primary.500' }}
              _before={{ bg: 'primary.500' }}
              _hover={{ color: '#e97a5b' }}
              fontSize={'md'}
              px={'5'}
              py={'7'}
              color="black"
              minW="fit"
              fontWeight={'600'}
            >
              Create New Booking
            </Tabs.Trigger>

            <Tabs.Trigger
              value={'existingBooking'}
              textTransform={'capitalize'}
              _selected={{ color: 'primary.500' }}
              _before={{ bg: 'primary.500' }}
              _hover={{ color: '#e97a5b' }}
              fontSize={'md'}
              px={'5'}
              py={'7'}
              color="black"
              minW="fit"
              fontWeight={'600'}
            >
              Select Existing Booking
            </Tabs.Trigger>
          </Tabs.List>
        </Box>

        <Box pt={'3'} paddingRight={'2'} paddingLeft={'2'} flex={1} pb={'20'}>
          <Tabs.Content value={'newBooking'}>
            <Box>
              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  handleSubmit();
                }}
              >
                <Stack gap={'1rem'}>
                  <CustomSelect
                    placeholder="Select Service"
                    required={true}
                    onChange={(val) => setFieldValue('service_id', val?.value)}
                    options={serviceOptions}
                    label="Service"
                    name="service_id"
                    errors={errors}
                  />

                  <StringInput
                    inputProps={{
                      name: 'appointment_date',
                      type: 'datetime-local',
                      placeholder: 'Select Date and Time',
                      onBlur: handleBlur,
                      value: values.appointment_date,
                      onChange: handleChange,
                    }}
                    fieldProps={{
                      label: 'Appointment Date & Time',
                      required: true,
                      invalid:
                        touched.appointment_date && !!errors.appointment_date,
                      errorText: String(errors.appointment_date),
                    }}
                  />

                  <CustomSelect
                    placeholder="Select Duration"
                    required={true}
                    onChange={(val) => setFieldValue('duration', val?.value)}
                    options={durationOptions}
                    label="Duration"
                    name="duration"
                    defaultValue={durationOptions.find(
                      (item) => item.value === values.duration
                    )}
                    errors={errors}
                  />

                  <Button
                    disabled={Object.keys(errors).length > 0}
                    w={'100%'}
                    type="submit"
                    loading={false}
                  >
                    Create Booking
                  </Button>
                </Stack>
              </form>
            </Box>
          </Tabs.Content>

          <Tabs.Content value={'existingBooking'}>
            <ExistingBookingSelector
              selectedClient={selectedClient}
              onBookingSelected={onBookingSelected}
            />
          </Tabs.Content>
        </Box>
      </Tabs.Root>
    </>
  );
};

// Component for selecting existing bookings
const ExistingBookingSelector: React.FC<{
  selectedClient?: any;
  onBookingSelected?: (booking: any) => void;
}> = ({ selectedClient, onBookingSelected }) => {
  const [selectedBooking, setSelectedBooking] = useState<any>(null);

  // Mock existing bookings - replace with actual API call
  const existingBookings = [
    {
      id: '1',
      client_name: 'John Doe',
      service_name: 'Speech Therapy Session',
      appointment_date: '2024-01-15T10:00',
      status: 'scheduled',
    },
    {
      id: '2',
      client_name: 'Jane Smith',
      service_name: 'Language Assessment',
      appointment_date: '2024-01-16T14:30',
      status: 'completed',
    },
    {
      id: '3',
      client_name: 'Bob Johnson',
      service_name: 'Consultation',
      appointment_date: '2024-01-17T09:00',
      status: 'scheduled',
    },
  ];

  const bookingOptions = existingBookings.map((booking) => ({
    label: `${booking.client_name} - ${booking.service_name} (${moment(
      booking.appointment_date
    ).format('MMM DD, YYYY h:mm A')})`,
    value: booking.id,
  }));

  const handleBookingSelect = () => {
    if (selectedBooking && onBookingSelected) {
      const booking = existingBookings.find((b) => b.id === selectedBooking);
      onBookingSelected(booking);
    }
  };

  return (
    <Box>
      <Stack gap={'1rem'}>
        <Text fontSize="sm" color="gray.600">
          Select an existing booking to link to this invoice
        </Text>

        <CustomSelect
          placeholder="Select Existing Booking"
          required={true}
          onChange={(val) => setSelectedBooking(val?.value)}
          options={bookingOptions}
          label="Existing Bookings"
          name="existing_booking"
        />

        {selectedBooking && (
          <Box p={4} bg="gray.50" borderRadius="md">
            <Text fontSize="sm" fontWeight="bold" mb={2}>
              Selected Booking Details:
            </Text>
            {(() => {
              const booking = existingBookings.find(
                (b) => b.id === selectedBooking
              );
              return booking ? (
                <Stack gap={1} fontSize="sm">
                  <Text>
                    <strong>Client:</strong> {booking.client_name}
                  </Text>
                  <Text>
                    <strong>Service:</strong> {booking.service_name}
                  </Text>
                  <Text>
                    <strong>Date:</strong>{' '}
                    {moment(booking.appointment_date).format(
                      'MMM DD, YYYY h:mm A'
                    )}
                  </Text>
                  <Text>
                    <strong>Status:</strong>{' '}
                    <span style={{ textTransform: 'capitalize' }}>
                      {booking.status}
                    </span>
                  </Text>
                </Stack>
              ) : null;
            })()}
          </Box>
        )}

        <Button
          disabled={!selectedBooking}
          w={'100%'}
          onClick={handleBookingSelect}
          loading={false}
        >
          Link Selected Booking
        </Button>
      </Stack>
    </Box>
  );
};

export default LinkBooking;


