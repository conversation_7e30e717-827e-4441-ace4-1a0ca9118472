import React from 'react';
import Invoices from './Invoices';
import { Metadata } from 'next';
import { cookies } from 'next/headers';
import { Box } from '@chakra-ui/react';

export async function generateMetadata(): Promise<Metadata> {
  const cookieStore = cookies();
  const cookieRaw = cookieStore.get('user_data')?.value;

  let organization_name = 'Soap'; // fallback
  if (cookieRaw) {
    try {
      const parsed = JSON.parse(cookieRaw);
      organization_name = parsed?.organization?.name || 'Soap';
    } catch (err) {
      console.warn('Failed to parse cookie user_data:', err);
    }
  }

  const fullTitle = `SOAP - ${organization_name} - Invoices`;
  const description = `Soap Note platform.`;

  return {
    title: fullTitle,
    description,
  };
}

export default function page() {
  return (
    <Box>
      <Invoices />
    </Box>
  );
}
