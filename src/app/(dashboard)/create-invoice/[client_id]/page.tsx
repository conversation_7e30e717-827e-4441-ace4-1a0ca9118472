import React from 'react';
import { Metadata } from 'next';
import { cookies } from 'next/headers';
// import CreateInvoiceItem from '../CreateInvoiceItem';
import InvoiceForm from '@/components/form/invoice/InvoiceForm';

export async function generateMetadata(): Promise<Metadata> {
  const cookieStore = cookies();
  const cookieRaw = cookieStore.get('user_data')?.value;

  let organization_name = 'Soap'; // fallback
  if (cookieRaw) {
    try {
      const parsed = JSON.parse(cookieRaw);
      organization_name = parsed?.organization?.name || 'Soap';
    } catch (err) {
      console.warn('Failed to parse cookie user_data:', err);
    }
  }

  const fullTitle = `Soap - ${organization_name} - Dashboard`;
  const description = `Soap Note platform.`;

  return {
    title: fullTitle,
    description,
  };
}

export default async function page() {
  return (
    <div>
      <InvoiceForm />
    </div>
  );
}
