import NumberCom from '@/app/(dashboard)/admin/forms/_components/NumberCom';
import TextCom from '@/app/(dashboard)/admin/forms/_components/TextCom';
import { CustomModal } from '@/components/elements/modal/custom-modal';
import { Box, Flex, Stack, Text } from '@chakra-ui/react';
import { QuestionTypes } from '../../../../../components/form/type';
import DateCom from '../_components/DateCom';
import RadioCom from '../_components/RadioCom';
import DndProviderWrapper from './DndProviderWrapper';
import SelectedQuestions from './SelectedQuestions';
const Questions = ({ formHook }: { formHook?: any }) => {
  const {
    open,
    onClose,
    generateUniqueId,
    handleQuestionClick,
    selectedQuestionType,
    allSelectedQuestions,
    removeSelectedQuestion,
    setAllSelectedQuestions,
  } = formHook;

  const renderQuestionModalBody = (q: any) => {
    if (!q) return null;

    // console.log('q---44', q);

    console.log('allSelected', allSelectedQuestions);
    switch (q.type.trim()) {
      case 'Textbox': // Removed the extra space
      case 'TextArea':
        return (
          <TextCom
            onClose={onClose}
            generateUniqueId={generateUniqueId}
            q={q}
            setAllSelectedQuestions={setAllSelectedQuestions}
          />
        );
      case 'Number':
        return (
          <NumberCom
            onClose={onClose}
            q={q}
            generateUniqueId={generateUniqueId}
            setAllSelectedQuestions={setAllSelectedQuestions}
          />
        );
      case 'Date':
        return (
          <DateCom
            onClose={onClose}
            q={q}
            setAllSelectedQuestions={setAllSelectedQuestions}
            generateUniqueId={generateUniqueId}
          />
        );
      case 'Single choice':
      case 'Multiple choice':
        return (
          <RadioCom
            onClose={onClose}
            q={q}
            setAllSelectedQuestions={setAllSelectedQuestions}
            generateUniqueId={generateUniqueId}
          />
        );
      default:
        return <Text>Select a question type.</Text>;
    }
  };
  return (
    <DndProviderWrapper>
      <Box
        display={'flex'}
        width={'100%'}
        flexDirection={'column'}
        justifyContent={'start'}
        alignItems={'start'}
        gap={'1rem'}
      >
        <Text>Add Questions :</Text>

        <Flex
          justifyContent={'space-between'}
          mt={'1rem'}
          alignItems={'start'}
          width={'100%'}
        >
          <Flex width={'100%'} gap={'1rem'}>
            <Stack w={'60%'}>
              {QuestionTypes?.map((question: any, index: any) => (
                <Box onClick={() => handleQuestionClick(question)} key={index}>
                  <Box
                    display={'flex'}
                    width={'100%'}
                    bg={'#F2F2F2'}
                    p={'1rem'}
                    gap={'10px'}
                    borderRadius={'5px'}
                    cursor={'pointer'}
                    alignItems={'center'}
                    transition="background-color 0.5s ease, transform 0.5s ease"
                    _hover={{ bg: 'primary.500', color: '#fff' }}
                  >
                    <Text>{question?.icon}</Text>
                    <Text>{question?.heading}</Text>
                  </Box>
                  {/* <Question question={question} /> */}
                </Box>
              ))}
            </Stack>

            <Box w={'40%'} maxW={'25rem'} maxH={'50vh'} overflow={'auto'}>
              <SelectedQuestions
                handleQuestionClick={handleQuestionClick}
                removeSelectedQuestion={removeSelectedQuestion}
                allSelectedQuestions={allSelectedQuestions}
                setAllSelectedQuestions={setAllSelectedQuestions}
              />
            </Box>
          </Flex>
        </Flex>

        {/* modal to show the selected question */}
        <CustomModal
          w={{ base: '30%', md: '9rem' }}
          open={open}
          onOpenChange={onClose}
        >
          <Box width={'100%'}>
            {renderQuestionModalBody(selectedQuestionType)}
          </Box>
        </CustomModal>
      </Box>
    </DndProviderWrapper>
  );
};

export default Questions;
