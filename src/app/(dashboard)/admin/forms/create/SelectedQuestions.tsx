import { Box, Text } from '@chakra-ui/react';
import React, { useState } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import { CiEdit, CiTextAlignRight } from 'react-icons/ci';
import { MdOutlineDelete } from 'react-icons/md';
import { RxTextAlignJustify } from 'react-icons/rx';
import iconMap from '../_components/iconMap';

interface Question {
  id: number;
  qt: string;
  type: string;
  required: boolean;
  icon: string;
  isDefault?: boolean;
  [key: string]: any;
}

interface SelectedQuestionsProps {
  allSelectedQuestions: Question[];
  removeSelectedQuestion: (id: number) => void;
  handleQuestionClick: (question: Question) => void;
  setAllSelectedQuestions: (
    questions: Question[] | ((prev: Question[]) => Question[])
  ) => void;
}

const ItemTypes = {
  QUESTION: 'question',
};

const DraggableQuestion: React.FC<{
  q: Question;
  index: number;
  removeSelectedQuestion: (id: number) => void;
  handleQuestionClick: (question: Question) => void;
  moveQuestion: (dragIndex: number, hoverIndex: number) => void;
}> = ({
  q,
  index,
  removeSelectedQuestion,
  handleQuestionClick,
  moveQuestion,
}) => {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const isDefault = q.default;

  // console.log('hello', isDefault);
  const ref = React.useRef<HTMLDivElement>(null);

  const [{ isDragging }, drag] = useDrag({
    type: ItemTypes.QUESTION,
    item: { index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const [, drop] = useDrop({
    accept: ItemTypes.QUESTION,
    hover(item: { index: number }, monitor) {
      if (!ref.current) return;
      const dragIndex = item.index;
      const hoverIndex = index;

      if (dragIndex === hoverIndex) return;

      const hoverBoundingRect = ref.current.getBoundingClientRect();
      const hoverMiddleY =
        (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
      const clientOffset = monitor.getClientOffset();

      if (!clientOffset) return;

      const hoverClientY = clientOffset.y - hoverBoundingRect.top;

      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) return;
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) return;

      moveQuestion(dragIndex, hoverIndex);
      item.index = hoverIndex;
    },
  });

  drag(drop(ref));

  return isDefault === 'true' ? (
    // Greyed-out version for default questions
    <Box
      ref={ref}
      position="relative"
      p={4}
      borderWidth="1px"
      mb={'10px'}
      borderRadius="md"
      boxShadow="md"
      bg="gray.100"
      w="100%"
      opacity={isDragging ? 0.5 : 1}
      cursor={isDragging ? 'grabbing' : 'move'}
    >
      <Text fontWeight="bold" fontSize="lg">
        {iconMap[q.icon] || null}
      </Text>
      <Text color="gray.500">{q?.qt}</Text>
      <Text fontSize="sm" color="gray.400">
        {q?.required ? 'Required' : 'Optional'} - {q?.heading} (Default)
      </Text>
    </Box>
  ) : (
    // Normal interactive version for non-default questions
    <Box
      ref={ref}
      position="relative"
      p={4}
      borderWidth="1px"
      mb={'10px'}
      borderRadius="md"
      boxShadow="md"
      bg="white"
      w="100%"
      opacity={isDragging ? 0.5 : 1}
      onMouseEnter={() => setHoveredIndex(index)}
      onMouseLeave={() => setHoveredIndex(null)}
      _hover={{
        bg: 'primary.500',
        color: '#fff',
        cursor: isDragging ? 'grabbing' : 'move',
      }}
    >
      {hoveredIndex === index && (
        <Box position="absolute" top={2} right={2} display="flex" gap={2}>
          <Box
            onClick={(e) => {
              e.stopPropagation();
              handleQuestionClick(q);
            }}
            p={1}
            borderRadius="md"
            color={'#000'}
            bg="#f2f2f2"
            _hover={{ bg: '#e0e0e0' }}
            cursor="pointer"
          >
            <CiEdit size={16} />
          </Box>
          <Box
            onClick={(e) => {
              e.stopPropagation();
              removeSelectedQuestion(q.id);
            }}
            p={1}
            borderRadius="md"
            color={'#000'}
            bg="#f2f2f2"
            _hover={{ bg: '#e0e0e0' }}
            cursor="pointer"
          >
            <MdOutlineDelete size={16} />
          </Box>
        </Box>
      )}
      <Text fontWeight="bold" fontSize="lg">
        {hoveredIndex === index ? (
          <RxTextAlignJustify size={20} />
        ) : (
          iconMap[q.icon] || null
        )}
      </Text>
      <Text>{q?.qt}</Text>
      <Text fontSize="sm" color="gray.500">
        {q?.required ? 'Required' : 'Optional'} - {q?.heading}
      </Text>
    </Box>
  );
};
const SelectedQuestions: React.FC<SelectedQuestionsProps> = ({
  allSelectedQuestions,
  removeSelectedQuestion,
  handleQuestionClick,
  setAllSelectedQuestions,
}) => {
  const moveQuestion = React.useCallback(
    (dragIndex: number, hoverIndex: number) => {
      setAllSelectedQuestions((prev) => {
        const newItems = [...prev];
        const [removed] = newItems.splice(dragIndex, 1);
        newItems.splice(hoverIndex, 0, removed);
        return newItems;
      });
    },
    [setAllSelectedQuestions]
  );

  return (
    <Box>
      {allSelectedQuestions?.length === 0 ? (
        <Box
          border="2px dotted"
          borderRadius="15px"
          padding="20px"
          display="flex"
          justifyContent="center"
          flexDirection={'column'}
          alignItems="center"
          height="200px"
          borderColor={'#f2f2f2'}
          textAlign={'center'}
          width={'100%'}
          gap={'1rem'}
        >
          <CiTextAlignRight />
          <Text fontWeight={500}>Add a question to start!</Text>
          <Text fontWeight={200}>{"Don't be shy, you are doing great."}</Text>
        </Box>
      ) : (
        allSelectedQuestions?.map((q, index) => (
          <DraggableQuestion
            key={`question-${q.id}-${index}`}
            index={index}
            q={q}
            moveQuestion={moveQuestion}
            removeSelectedQuestion={removeSelectedQuestion}
            handleQuestionClick={handleQuestionClick}
          />
        ))
      )}
    </Box>
  );
};

export default SelectedQuestions;
