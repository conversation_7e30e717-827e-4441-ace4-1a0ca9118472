import React from 'react';
import AllSlps from './all-slps/AllSlps';
import { Metadata } from 'next';
import { cookies } from 'next/headers';
import { Box } from '@chakra-ui/react';

export async function generateMetadata(): Promise<Metadata> {
  const cookieStore = cookies();
  const cookieData: any = cookieStore.get('user_data')?.value;
  const organization_name =
    JSON.parse(cookieData)?.organization?.name || 'Soap';

  const fullTitle = `Soap - ${organization_name} - SLPs`;
  const description = `Soap Note platform.`;

  return {
    title: fullTitle,
    description,
  };
}

export default function page() {
  return (
    <Box>
      <AllSlps />
    </Box>
  );
}
