'use server';
import { NextResponse } from 'next/server';
import { tableNames } from '@/constants/table_names';
import { createSupabaseServer } from '@/lib/supabase/server';

// Define a GET handler for fetching linkable bookings by client ID
// Linkable bookings are those that either:
// 1. Don't have slp_notes, OR
// 2. Have slp_notes but slp_notes doesn't have an invoice_id
export async function GET(
  request: Request,
  { params }: { params: { client_id: string } }
) {
  const supabase = createSupabaseServer();
  const { client_id } = params;

  try {
    // First, get bookings without slp_notes
    const { data: bookingsWithoutNotes, error: error1 } = await supabase
      .from(tableNames.bookings)
      .select(
        `
        *,
        clients(*),
        slp_notes!inner(
          id,
          booking_id,
          client_id,
          invoice_id,
          status,
          note_date
        )
      `
      )
      .eq('client_id', client_id)
      .is('slp_notes', null);

    if (error1) {
      throw new Error(error1.message);
    }

    // Second, get bookings with slp_notes but without invoice_id
    const { data: bookingsWithNotesNoInvoice, error: error2 } = await supabase
      .from(tableNames.bookings)
      .select(
        `
        *,
        clients(*),
        slp_notes!inner(
          id,
          booking_id,
          client_id,
          invoice_id,
          status,
          note_date
        )
      `
      )
      .eq('client_id', client_id)
      .is('slp_notes.invoice_id', null);

    if (error2) {
      throw new Error(error2.message);
    }

    // Combine both results
    const linkableBookings = [
      ...(bookingsWithoutNotes || []),
      ...(bookingsWithNotesNoInvoice || []),
    ];

    // Sort by appointment date (most recent first)
    linkableBookings.sort((a, b) => {
      const dateA = new Date(a.appointment || '');
      const dateB = new Date(b.appointment || '');
      return dateB.getTime() - dateA.getTime();
    });

    return NextResponse.json(
      {
        data: linkableBookings,
        count: linkableBookings.length,
      },
      { status: 200 }
    );
  } catch (error: any) {
    console.error('Error fetching linkable bookings:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch linkable bookings' },
      { status: 500 }
    );
  }
}
