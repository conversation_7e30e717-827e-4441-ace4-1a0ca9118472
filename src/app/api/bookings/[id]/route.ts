'use server';
import { NextRequest, NextResponse } from 'next/server';
import { tableNames } from '@/constants/table_names';
import { createSupabaseServer } from '@/lib/supabase/server';

// Define a GET handler for fetching the client by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const { id } = params;
  const supabase = createSupabaseServer();

  const { data, error } = await supabase
    .from(tableNames.bookings)
    .select(
      `*, 
      clients(*,  packages(*), invoices(*)), 
      slp_notes(*,invoice:invoices!slp_notes_invoice_id_fkey(*, slp_data:users(*),services(*), transactions:transactions_invoice_id_fkey(*))),
      services_purchases(*, invoice_items(*, invoice_id(*), services(*)))
      
      `
      // package_used:redeemed_sessions(*),
    )
    .eq('id', id);
  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }

  return NextResponse.json(data);
}

// Define a PATCH handler for fetching the client by ID
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const { id } = params;
  const supabase = await createSupabaseServer();

  const { payload } = await request.json();
  if (!id || !payload) {
    return NextResponse.json(
      { message: 'ID and data are required' },
      { status: 400 }
    );
  }
  console.log('payload is ', payload);

  const { data: updatedClient, error } = await supabase
    .from(tableNames.bookings)
    .update(payload)
    .eq('id', Number(id))
    .select();
  console.log('error', error);

  console.log('error is', error);
  if (error) {
    console.log('error', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
  return NextResponse.json(updatedClient?.[0]);
}
