//OPTIMIZED
import { MutationConfig, useMutation } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { queryKey } from '@/constants/query-key';
import { toaster } from '@/components/ui/toaster';

const createAnswers = async (body: any) => {
  const response = await fetch(`/api/public/form-answers`, {
    method: 'POST',
    body: JSON.stringify(body),
  });
  if (!response.ok) throw new Error('Error creating answers');
  return response.json();
};

type QueryFnType = typeof createAnswers;

export const useCreateAnswersMutation = (
  config?: MutationConfig<QueryFnType>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    // onSuccess: () => {
    //   toast({
    //     status: 'success',
    //     description: ToastMessages.bookings.createSuccess,
    //   });
    // },
    retry: false,
    mutationKey: [queryKey.forms.uploadFormAnswers],
    mutationFn: createAnswers,
    ...config,
  });
};
