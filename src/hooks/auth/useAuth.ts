/* eslint-disable react-hooks/exhaustive-deps */
import { useGetUserByEmailQuery } from '@/api/users/get-user-by-email';
import { env } from '@/constants/env';
import supabase, { createSupabaseClient } from '@/lib/supabase/client';
import { IUser } from '@/shared/interface/user';
import { UserState } from '@/store/user/user';
import { useSearchParams } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';
import { useRecoilState } from 'recoil';
// import { clearUser } from '@/utils/get-user';
import { tableNames } from '@/constants/table_names';
import { deleteCookie } from '@/utils/cookie-helper';
import moment from 'moment';

export const useAuth = () => {
  const [email, setEmail] = useState<string | undefined>('');
  const { data, isSuccess } = useGetUserByEmailQuery(email!, {
    enabled: <PERSON><PERSON><PERSON>(email),
  });
  const [userState, setUserState] = useRecoilState(UserState);
  const searchParams = useSearchParams();
  const nextPath = searchParams.get('next');

  const [user, setUser] = useState<IUser | any | null | undefined>(undefined);

  const resetUserState = useCallback(() => {
    setUserState({
      email: '',
      role: '',
      id: undefined,
      first_name: '',
      last_name: '',
      pay_rate: undefined,
      registration: '',
      status: '',
    });
    setUser(null);
    setEmail('');
  }, [setUserState]);

  const updateUserLoginDate = useCallback(async () => {
    if (!data?.id) return;

    try {
      const currentDateTime = moment().toISOString(); // Using moment to get ISO string

      const { error } = await supabase
        .from(tableNames.users)
        .update({ last_login_dt: currentDateTime })
        .eq('id', data.id);

      if (error) {
        console.error('Error updating last login date:', error);
        return;
      }

      console.log('Last login date updated successfully');
    } catch (error) {
      console.error('Failed to update last login date:', error);
    }
  }, [data?.id]);

  const handleSignIn = useCallback(async () => {
    // Reset all state first
    resetUserState();

    // Clear Supabase session
    await supabase.auth.signOut();

    // Clear storage
    window.localStorage.clear();
    deleteCookie('user_data');

    // Then proceed with fresh login
    await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${env.FRONTEND_URL}/auth/callback?next=${nextPath}`,
      },
    });
  }, [nextPath, resetUserState]);

  const handleSignOut = useCallback(async () => {
    resetUserState();
    await supabase.auth.signOut();
    window.localStorage.clear();
    deleteCookie('user_data');
    window.location.href = '/login';
  }, [resetUserState]);

  useEffect(() => {
    async function getUser() {
      // Don't return early based on persisted state alone
      const supabase = createSupabaseClient();
      const {
        data: { session },
      } = await supabase.auth.getSession();

      if (
        userState.id &&
        session?.user &&
        userState.email === session.user.email
      ) {
        return; // Valid session matches persisted state
      }

      const { data, error } = await supabase.auth.getUser();
      if (error || !data?.user) {
        resetUserState();
      } else {
        setUser(data.user);
        setEmail(data.user.email);
      }
    }
    getUser();
  }, [userState.id, userState.email, resetUserState]);

  // useEffect(() => {
  //   async function getUser() {
  //     if (userState.id) return;
  //     const supabase = createSupabaseClient();
  //     const { data, error } = await supabase.auth.getUser();
  //     if (error || !data?.user) {
  //       setUser(null);
  //     } else {
  //       setUser(data.user);
  //       // console.log('data user is', data.user);
  //       setEmail(data.user.email);
  //     }
  //   }
  //   getUser();
  // }, []);

  useEffect(() => {
    if (isSuccess && data) {
      setUserState(data);

      updateUserLoginDate();
    }
  }, [isSuccess, data]);

  return { handleSignOut, handleSignIn, userState, user };
};
