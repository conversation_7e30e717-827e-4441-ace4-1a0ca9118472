import { useState, useMemo } from 'react';
import { useFormik } from 'formik';
import * as yup from 'yup';
import moment from 'moment';
import { useGetServicesQuery } from '@/api/services/get-services-by-slp';
import { useGetAllSlpQuery } from '@/api/users/get-slps';
import { useGetLinkableBookingsQuery } from '@/api/bookings/get-linkable-bookings';
import { useSupabaseSession } from '@/hooks/auth/useUserSession';
import { toaster } from '@/components/ui/toaster';

// Validation schema for new booking
const newBookingSchema = yup.object().shape({
  service_id: yup.string().required('Service selection is required'),
  assigned_to: yup.string().required('Assigned SLP is required'),
  appointment: yup
    .string()
    .test('valid-date', 'Invalid date format', (value) =>
      moment(value, 'YYYY-MM-DDTHH:mm', true).isValid()
    )
    .required('Appointment date is required'),
  event: yup.string().required('Service name is required'),
});

interface UseLinkBookingHookProps {
  purchase?: any;
  onBookingCreated?: (booking: any) => void;
  onBookingSelected?: (booking: any) => void;
  onClose?: () => void;
}

export const useLinkBookingHook = ({
  purchase,
  onBookingCreated,
  onBookingSelected,
  onClose,
}: UseLinkBookingHookProps) => {
  const [activeTab, setActiveTab] = useState('newBooking');
  const [selectedExistingBooking, setSelectedExistingBooking] =
    useState<any>(null);
  const [loading, setLoading] = useState(false);

  const { UserFromQuery } = useSupabaseSession();
  const raw = localStorage.getItem('UserState');
  const dataOrg = raw ? JSON.parse(raw) : null;
  const org = dataOrg?.UserState?.organization;

  const searchParams = new URLSearchParams(window.location.search);
  const orgIdFromUrl = searchParams.get('organization_id');

  // Get services data
  const { data: ServicesData } = useGetServicesQuery(
    dataOrg?.UserState?.organization_id || UserFromQuery?.organization_id,
    {
      enabled:
        Boolean(dataOrg?.UserState?.organization_id) ||
        Boolean(UserFromQuery?.organization_id),
    }
  );

  // Get SLP data
  const { data: slpData, isLoading: slpLoading } = useGetAllSlpQuery({
    organization_id: orgIdFromUrl ? Number(orgIdFromUrl) : org?.id,
  });

  // Get linkable bookings for the selected client
  const { data: linkableBookingsData, isLoading: linkableBookingsLoading } =
    useGetLinkableBookingsQuery(purchase?.client_id, {
      enabled: !!purchase?.client_id && activeTab === 'existingBooking',
    });

  // Transform services data for dropdown
  const servicesOption = useMemo(
    () =>
      ServicesData?.services?.map((item: any) => ({
        label: item?.name,
        value: item,
      })) || [],
    [ServicesData]
  );

  // Transform SLP data for dropdown
  const SlpOptions = useMemo(() => {
    return slpData
      ? slpData
          .filter((user: any) => user.status === 'Active')
          .map((item: any) => ({
            label: `${item?.first_name} ${item?.last_name}`,
            value: item?.email,
            id: item?.id,
          }))
      : [];
  }, [slpData]);

  // Transform linkable bookings for dropdown
  const existingBookingOptions = useMemo(() => {
    if (!linkableBookingsData?.data) return [];

    return linkableBookingsData.data.map((booking: any) => ({
      label: `${booking.event || 'Unknown Service'} - ${moment(
        booking.appointment
      ).format(
        'MMM DD, YYYY h:mm A'
      )} ${booking.slp_notes ? '(Has Notes)' : '(No Notes)'}`,
      value: booking.id,
      booking: booking,
    }));
  }, [linkableBookingsData]);

  // Form for creating new booking
  const {
    values,
    handleBlur,
    handleChange,
    setFieldValue,
    errors,
    touched,
    handleSubmit,
    resetForm,
  } = useFormik({
    initialValues: {
      client_id: purchase?.client_id || '',
      service_id: '',
      assigned_to: '',
      appointment: '',
      event: '',
    },
    validationSchema: newBookingSchema,
    onSubmit: async (values) => {
      try {
        setLoading(true);
        console.log('Creating new booking with values:', values);

        // TODO: Implement actual booking creation API call
        // For now, we'll simulate the creation
        const newBooking = {
          ...values,
          id: Date.now(), // Temporary ID
          client_id: purchase?.client_id,
          created_at: new Date().toISOString(),
        };

        toaster.create({
          description: 'Booking created successfully!',
          type: 'success',
        });

        if (onBookingCreated) {
          onBookingCreated(newBooking);
        }

        resetForm();
        onClose?.();
      } catch (error: any) {
        toaster.create({
          description: error.message || 'Failed to create booking',
          type: 'error',
        });
      } finally {
        setLoading(false);
      }
    },
  });

  const handleValueChange = (details: { value: string }) => {
    setActiveTab(details.value);
  };

  const handleExistingBookingSelect = () => {
    if (selectedExistingBooking && onBookingSelected) {
      const bookingOption = existingBookingOptions.find(
        (option: any) => option.value === selectedExistingBooking
      );
      if (bookingOption) {
        onBookingSelected(bookingOption.booking);
        onClose?.();
      }
    }
  };

  return {
    // State
    activeTab,
    selectedExistingBooking,
    loading,
    slpLoading,
    linkableBookingsLoading,

    // Data
    servicesOption,
    SlpOptions,
    existingBookingOptions,
    linkableBookingsData,

    // Form
    values,
    handleBlur,
    handleChange,
    setFieldValue,
    errors,
    touched,
    handleSubmit,

    // Handlers
    handleValueChange,
    setSelectedExistingBooking,
    handleExistingBookingSelect,
  };
};
