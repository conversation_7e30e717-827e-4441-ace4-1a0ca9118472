/* eslint-disable react-hooks/exhaustive-deps */
import { useState, useMemo, useEffect } from 'react';
import { useFormik } from 'formik';
import * as yup from 'yup';
import moment from 'moment';
import { useGetServicesQuery } from '@/api/services/get-services-by-slp';
import { useGetAllSlpQuery } from '@/api/users/get-slps';
import { useGetLinkableBookingsQuery } from '@/api/bookings/get-linkable-bookings';
import { useLinkBookingMutation } from '@/api/bookings/link-booking';
import { useSupabaseSession } from '@/hooks/auth/useUserSession';
// import { toaster } from '@/components/ui/toaster';

// Validation schema for new booking
const newBookingSchema = yup.object().shape({
  service_id: yup.string().required('Service selection is required'),
  assigned_to: yup.string().required('Assigned SLP is required'),
  appointment: yup
    .string()
    .test('valid-date', 'Invalid date format', (value) =>
      moment(value, 'YYYY-MM-DDTHH:mm', true).isValid()
    )
    .required('Appointment date is required'),
  event: yup.string().required('Service name is required'),
});

interface UseLinkBookingHookProps {
  purchase?: any;
  invoice?: any;
  onBookingCreated?: (booking: any) => void;
  onBookingSelected?: (booking: any) => void;
  onClose?: () => void;
}

export const useLinkBookingHook = ({
  purchase,
  invoice,
  onBookingCreated,
}: UseLinkBookingHookProps) => {
  const [activeTab, setActiveTab] = useState('newBooking');
  const [selectedExistingBooking, setSelectedExistingBooking] =
    useState<any>(null);
  const [selectedBookingData, setSelectedBookingData] = useState<any>(null);

  const { UserFromQuery } = useSupabaseSession();
  const raw = localStorage.getItem('UserState');
  const dataOrg = raw ? JSON.parse(raw) : null;
  const org = dataOrg?.UserState?.organization;

  const searchParams = new URLSearchParams(window.location.search);
  const orgIdFromUrl = searchParams.get('organization_id');

  // Get services data
  const { data: ServicesData } = useGetServicesQuery(
    dataOrg?.UserState?.organization_id || UserFromQuery?.organization_id,
    {
      enabled:
        Boolean(dataOrg?.UserState?.organization_id) ||
        Boolean(UserFromQuery?.organization_id),
    }
  );

  // Get SLP data
  const { data: slpData, isLoading: slpLoading } = useGetAllSlpQuery({
    organization_id: orgIdFromUrl ? Number(orgIdFromUrl) : org?.id,
  });

  // Get linkable bookings for the selected client
  const { data: linkableBookingsData, isLoading: linkableBookingsLoading } =
    useGetLinkableBookingsQuery(purchase?.client_id, {
      enabled: !!purchase?.client_id && activeTab === 'existingBooking',
    });

  // Link booking mutation
  const { mutateAsync: linkBooking, isLoading: linkBookingLoading } =
    useLinkBookingMutation();

  // Transform services data for dropdown
  const servicesOption = useMemo(
    () =>
      ServicesData?.services?.map((item: any) => ({
        label: item?.name,
        value: item,
      })) || [],
    [ServicesData]
  );

  // Transform SLP data for dropdown
  const SlpOptions = useMemo(() => {
    return slpData
      ? slpData
          .filter((user: any) => user.status === 'Active')
          .map((item: any) => ({
            label: `${item?.first_name} ${item?.last_name}`,
            value: item?.email,
            id: item?.id,
          }))
      : [];
  }, [slpData]);

  // Transform linkable bookings for dropdown
  const existingBookingOptions = useMemo(() => {
    if (!linkableBookingsData?.data) return [];

    return linkableBookingsData.data.map((booking: any) => ({
      label: `${booking.event || 'Unknown Service'} - ${moment(
        booking.appointment
      ).format(
        'MMM DD, YYYY h:mm A'
      )} ${booking.slp_notes ? '(Has Notes)' : '(No Notes)'}`,
      value: booking.id,
      booking: booking,
    }));
  }, [linkableBookingsData]);

  // Form for creating new booking
  const {
    values,
    handleBlur,
    handleChange,
    setFieldValue,
    errors,
    setErrors,
    touched,
    handleSubmit,
    resetForm,
  } = useFormik({
    initialValues: {
      client_id: purchase?.client_id || '',
      service_id: purchase?.service_id || '',
      assigned_to: '',
      appointment: '',
      event: purchase?.service?.name || '',
    },
    validationSchema: newBookingSchema,
    onSubmit: async (values) => {
      try {
        console.log('Submitting booking with values:', values);

        const payload = {
          // Booking data
          booking_id: selectedBookingData?.id || null, // null for new booking
          client_id: purchase?.client_id,
          service_id: values.service_id,
          assigned_to: values.assigned_to,
          appointment: values.appointment,
          event: values.event,
          organization_id:
            dataOrg?.UserState?.organization_id ||
            UserFromQuery?.organization_id,
          user_id: UserFromQuery?.id,
          invoice_id: invoice?.id,

          // Purchase data (you may need to pass this from parent component)
          purchaseData: purchase,
        };

        const result = await linkBooking(payload);

        if (onBookingCreated) {
          onBookingCreated(result.data);
        }

        resetForm();
      } catch (error: any) {
        // Error handling is done in the mutation
        console.error('Error submitting booking:', error);
      }
    },
  });

  const handleValueChange = (details: { value: string }) => {
    setActiveTab(details.value);
  };

  const handleExistingBookingSelect = (value: any) => {
    const bookingOption = existingBookingOptions.find(
      (option: any) => option.value === value
    );
    if (bookingOption) {
      setSelectedExistingBooking(value);
      const booking = bookingOption.booking;
      setSelectedBookingData(booking);

      // Prefill form with existing booking data
      // setFieldValue('service_id', booking.service_id || booking.product_id);
      setFieldValue('assigned_to', booking.assigned_to);
      setFieldValue(
        'appointment',
        moment(booking.appointment || new Date()).format('YYYY-MM-DDTHH:mm')
      );
      setFieldValue('event', booking.event);
    }
  };

  useEffect(() => {
    setFieldValue('client_id', purchase?.client_id);
    setFieldValue('service_id', purchase?.service_id);
    setFieldValue('event', purchase?.service?.name);
    setFieldValue('appointment', moment().format('YYYY-MM-DDTHH:mm'));
  }, []);

  useEffect(() => {
    if (SlpOptions.length === 1) {
      setFieldValue('assigned_to', SlpOptions[0].value);
      setErrors({});
    }
  }, [SlpOptions, setFieldValue]);

  return {
    // State
    activeTab,
    selectedExistingBooking,
    loading: linkBookingLoading,
    slpLoading,
    linkableBookingsLoading,

    // Data
    servicesOption,
    SlpOptions,
    existingBookingOptions,
    linkableBookingsData,

    // Form
    values,
    handleBlur,
    handleChange,
    setFieldValue,
    errors,
    touched,
    handleSubmit,

    // Handlers
    handleValueChange,
    setSelectedExistingBooking,
    handleExistingBookingSelect,
  };
};
