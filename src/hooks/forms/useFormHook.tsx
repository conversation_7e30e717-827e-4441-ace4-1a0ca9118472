/* eslint-disable react-hooks/exhaustive-deps */

import { useGetFormByIdQuery } from '@/api/forms/get-form-by-id';
import NumberCom from '@/app/(dashboard)/admin/forms/_components/NumberCom';
import TextCom from '@/app/(dashboard)/admin/forms/_components/TextCom';
import { toaster } from '@/components/ui/toaster';
import { tableNames } from '@/constants/table_names';
import supabase from '@/lib/supabase/client';
import { getSlugFromName } from '@/utils/event';
import { Text, useDisclosure } from '@chakra-ui/react';
import { useQueryClient } from '@tanstack/react-query';
import { useFormik } from 'formik';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import * as Yup from 'yup';

import { useGetAllFormsQuery } from '@/api/forms/get-all-forms';
import { queryKey } from '@/constants/query-key';
import moment from 'moment';

import { useGetAllAnswersQuery } from '@/api/answers/get-all-answers';
import DateCom from '@/app/(dashboard)/admin/forms/_components/DateCom';
import RadioCom from '@/app/(dashboard)/admin/forms/_components/RadioCom';
import { IGetFormsFilterState } from '@/store/filters/forms';
import { useRecoilValue } from 'recoil';

export const useFormHook = ({ slp, id }: { slp?: any; id?: any }) => {
  const [loading, setLoading] = useState(false);

  const [selectedClient, setSelectedClient] = useState<any>();
  const [selectedFormFromClient, setSelectedFormFromClient] = useState<any>();
  const [showSearchClient, setShowSearchClient] = useState(true);

  const router = useRouter();
  const queryClient = useQueryClient();
  const filter = useRecoilValue(IGetFormsFilterState);

  const [formIdToBeDel, setFormIdToBeDel] = useState('');

  const generateUniqueId = () => {
    return `q-${moment().valueOf()}-${Math.floor(Math.random() * 900) + 100}`;
    // Example: "q-1712345678901-427"
  };

  const [allSelectedQuestions, setAllSelectedQuestions] = useState<any[]>([
    {
      icon: 'TfiText',
      id: generateUniqueId(),
      default: 'true',
      qt: 'Email',
      required: true,
      heading: 'Short answer',
      type: 'Textbox',
      title: '',
      description: '',
    },
  ]);

  console.log('allSelectedQuestions', allSelectedQuestions);

  const [selectedQuestionType, setSelectedQuestionType] = useState<
    string | null
  >(null);
  const path = usePathname();

  const searchParams = useSearchParams();
  const organizationId = searchParams.get('organization_id');
  const slp_id = path.split('/')[2];

  console.log('slp_id', slp_id);

  const raw = localStorage.getItem('UserState');
  const dataOrg = raw ? JSON.parse(raw) : null;
  const org = dataOrg?.UserState?.organization;

  // queries

  const { data: AllAnswers, isLoading: AllAnswersLoading } =
    useGetAllAnswersQuery({
      organizationId: organizationId ? Number(organizationId) : org?.id,
    });

  const { data: GetAllFormsData, isLoading: GetAllFormsLoading } =
    useGetAllFormsQuery({
      organizationId: organizationId ? Number(organizationId) : org?.id,
    });
  const { data: FormData, isLoading: FormDataIsloading } = useGetFormByIdQuery(
    Number(id),
    { enabled: Boolean(id) }
  );

  // console.log('user from server', slp);

  // console.log('allSekected', allSelectedQuestions);

  // disclosures

  const { onClose, onOpen, open } = useDisclosure();
  const {
    onClose: onCloseDelete,
    onOpen: onOpenDelete,
    open: openDelete,
  } = useDisclosure();
  const {
    onClose: onCloseLearn,
    onOpen: onOpenLearn,
    open: openLearn,
  } = useDisclosure();

  // FUNCTIONS

  const handleGetFormFromLoopedForms = (form: any) => {
    setSelectedFormFromClient(form);
  };

  const handleAddClient = (props: any) => {
    console.log('props', props);
    setSelectedClient(props);
    setShowSearchClient(false);
  };

  // console.log('allselectedq', allSelectedQuestions);

  const handleOpenLearnModal = () => onOpenLearn();

  const handleQuestionClick = (q: any) => {
    setSelectedQuestionType(q);
    onOpen();
  };
  // function to remove selected question from allSelectedQuestions
  const removeSelectedQuestion = (id: any) => {
    setAllSelectedQuestions((prev) => prev.filter((q) => q.id !== id));
  };
  const handleConfirmDeleteFormModal = (id: any) => {
    setFormIdToBeDel(id);
    onOpenDelete();
  };

  const handleDeleteForm = async (id: any) => {
    setLoading(true);
    try {
      const { error } = await supabase
        .from(tableNames.forms)
        .delete()
        .eq('id', Number(id));
      if (error) throw error;

      await queryClient.refetchQueries({
        queryKey: [queryKey.forms.getAllForms, filter],
      });
      toaster.create({
        description: 'Form Deleted Successfully',
        type: 'success',
      });
      onCloseDelete();
    } catch (error) {
      toaster.create({
        description: 'Something went wrong.',
        type: 'error',
      });
      setLoading(false);
    } finally {
      setLoading(false);
    }
  };

  const QuestionModalContent = ({ q }: { q: any }) => {
    switch (q.type) {
      case 'Textbox':
        return (
          <TextCom
            onClose={onClose}
            generateUniqueId={generateUniqueId}
            q={q}
            setAllSelectedQuestions={setAllSelectedQuestions}
          />
        );
      case 'Number':
        return (
          <NumberCom
            onClose={onClose}
            q={q}
            generateUniqueId={generateUniqueId}
            setAllSelectedQuestions={setAllSelectedQuestions}
          />
        );
      case 'Date':
        return (
          <DateCom
            onClose={onClose}
            q={q}
            setAllSelectedQuestions={setAllSelectedQuestions}
            generateUniqueId={generateUniqueId}
          />
        );
      case 'Single choice':
      case 'Multiple choice':
        return (
          <RadioCom
            onClose={onClose}
            q={q}
            setAllSelectedQuestions={setAllSelectedQuestions}
            generateUniqueId={generateUniqueId}
          />
        );
      default:
        return <Text>Select a question type.</Text>;
    }
  };
  // =========== Derived Data & Memos ===========

  const initialValues = {
    title: '',
    description: '',
    slug: '',
    organization_id: slp?.organization_id || slp?.organization?.id || '',
    organization_name: slp?.organization?.name || '',
  };

  const FormSchema = Yup.object().shape({
    title: Yup.string()
      .required('Title is required')
      .max(100, 'Title must be 100 characters or less'),
    description: Yup.string()
      .required('Description is required')
      .min(1, 'Description must be more than 1 character')
      .max(500, 'Description must be 500 characters or less'),
    slug: Yup.string()
      .required('URL path is required')
      .matches(
        /^[a-z0-9-]*$/,
        'URL path can only contain lowercase letters, numbers, and hyphens'
      )
      .test(
        'no-trailing-slash',
        'URL path cannot end with a hyphen',
        (value) => !value.endsWith('-')
      ),
  });

  const handleSubmitNewForm = () => {
    handleSubmit();
  };
  const {
    values,
    handleSubmit,
    errors,
    touched,
    handleChange,
    setFieldValue,
    setValues,
    setErrors,
  } = useFormik({
    initialValues: initialValues,
    validationSchema: FormSchema,
    onSubmit: async (values) => {
      try {
        setLoading(true);

        if (allSelectedQuestions?.length === 0) {
          toaster.create({
            description: 'Please add at least one question.',
            type: 'error',
          });
          setLoading(false);
          return;
        }

        const insert = {
          title: values.title,
          description: values.description,
          slug: values.slug || getSlugFromName(values.title),
          organization_name: slp
            ? getSlugFromName(slp?.organization?.name)
            : values?.organization_name,
          organization_id:
            Number(organizationId) ||
            slp?.organization_id ||
            values?.organization_id,
          questions: allSelectedQuestions,
        };

        if (!id || !FormData) {
          const { error } = await supabase
            .from(tableNames.forms)
            .insert(insert);
          if (error) throw error;

          toaster.create({
            description: 'Form Created Successfully',
            type: 'success',
          });
        } else {
          // Identify updated questions
          const updatedQuestions = allSelectedQuestions.filter(
            (newQ: any) =>
              !FormData.questions.some(
                (oldQ: any) =>
                  oldQ.id === newQ.id &&
                  JSON.stringify(oldQ) === JSON.stringify(newQ)
              )
          );

          // Identify deleted questions
          const deletedQuestionIds = FormData.questions
            .filter(
              (q: any) =>
                !allSelectedQuestions.some((selected) => selected.id === q.id)
            )
            .map((q: any) => q.id);

          // Update the form in the database
          const { error } = await supabase
            .from(tableNames.forms)
            .update(insert)
            .eq('id', Number(FormData?.id));
          if (error) throw error;

          if (updatedQuestions.length > 0 || deletedQuestionIds.length > 0) {
            // Fetch all form_answers for this form
            const { data: formAnswers, error: fetchError } = await supabase
              .from(tableNames.form_answers)
              .select('id, answer_details')
              .eq('form_id', Number(FormData?.id));

            if (fetchError) throw fetchError;

            if (formAnswers?.length > 0) {
              for (const row of formAnswers) {
                // Extract existing answers
                const existingAnswers = row.answer_details || [];

                // Update edited questions
                const updatedAnswers = existingAnswers.map((ans: any) =>
                  updatedQuestions.some((q) => q.id === ans.id)
                    ? {
                        ...ans,
                        ...updatedQuestions.find((q) => q.id === ans.id),
                      }
                    : ans
                );

                // Set `show_answers` to false for deleted questions
                const finalAnswers = updatedAnswers.map((ans: any) =>
                  deletedQuestionIds.includes(ans.id)
                    ? { ...ans, show_answers: 'false' }
                    : ans
                );

                // Update the database
                const { error: updateError } = await supabase
                  .from(tableNames.form_answers)
                  .update({ answer_details: finalAnswers }) // Update JSONB array
                  .eq('id', row.id); // Update specific row

                if (updateError) throw updateError;
              }
            }
          }

          toaster.create({
            description: 'Form Updated Successfully',
            type: 'success',
          });
        }

        await queryClient.refetchQueries({
          queryKey: [queryKey.forms.getAllForms, filter],
        });
        await queryClient.invalidateQueries({
          queryKey: [queryKey.forms.getFormById, Number(FormData?.id)],
        });
        await queryClient.refetchQueries({
          queryKey: [queryKey.forms.getFormAnswersById, Number(FormData?.id)],
        });
      } catch (error) {
        setLoading(false);
        toaster.create({
          description: 'Something went wrong.',
          type: 'error',
        });
        console.error(error);
      } finally {
        setLoading(false);

        if (organizationId && slp_id) {
          const url = `/slp/${slp_id}?view=dashboard&organization_id=${organizationId}&tab=forms`;
          router.replace(url);
        } else {
          router.replace('/admin/forms?view=dashboard');
        }
      }
    },
  });

  //   EFFECTS

  useEffect(() => {
    if (FormData || selectedFormFromClient) {
      setValues({
        title: FormData?.title || selectedFormFromClient?.title || '',

        description:
          FormData?.description || selectedFormFromClient?.description || '',
        slug: FormData?.slug || selectedFormFromClient?.slug || '',
        organization_id:
          Number(organizationId) ||
          FormData?.organization_id ||
          selectedFormFromClient?.organization_id,
        organization_name:
          FormData?.organization_name ||
          selectedFormFromClient?.organization_name,
      });

      setAllSelectedQuestions(
        FormData?.questions || selectedFormFromClient?.questions
      );
    }
  }, [FormData, id, selectedFormFromClient]);

  return {
    loading,
    values,
    errors,
    touched,
    handleChange,
    GetAllFormsData,
    handleQuestionClick,
    setErrors,
    selectedQuestionType,
    setFieldValue,
    AllAnswers: AllAnswers?.data?.allAnswers,
    AllAnswersLoading,
    onClose,
    handleDeleteForm,
    setAllSelectedQuestions,
    allSelectedQuestions,
    onOpen,
    QuestionModalContent,
    generateUniqueId,
    handleOpenLearnModal,
    handleAddClient,
    formIdToBeDel,
    handleGetFormFromLoopedForms,
    GetAllFormsLoading,
    handleConfirmDeleteFormModal,
    openDelete,
    onCloseDelete,
    removeSelectedQuestion,
    setValues,
    onOpenDelete,
    organizationName: slp?.organization?.name || '',
    FormDataIsloading,
    onCloseLearn,
    onOpenLearn,
    openLearn,
    showSearchClient,
    selectedClient,
    setShowSearchClient,
    open,
    handleSubmitNewForm,
    handleSubmit,
  };
};
