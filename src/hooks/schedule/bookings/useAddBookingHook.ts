/* eslint-disable react-hooks/exhaustive-deps */

import {
  checkDuplicateBooking,
  createClient,
  createClientEmails,
  findExistingClient,
} from '@/api/bookings';
import { useGetAllClientsQuery } from '@/api/clients/get-all-clients';
// import { useGetAllProductsQuery } from '@/api/products/get-all-products';
import { useGetServicesQuery } from '@/api/services/get-services-by-slp';
import { useGetAllSlpQuery } from '@/api/users/get-slps';
import { toaster } from '@/components/ui/toaster';
import { queryKey } from '@/constants/query-key';
import { tableNames } from '@/constants/table_names';
import { ToastMessages } from '@/constants/toast-messages';
import { useSupabaseSession } from '@/hooks/auth/useUserSession';
import supabase from '@/lib/supabase/client';
import { AllClientsFilterState } from '@/store/filters/clients';
import { useQueryClient } from '@tanstack/react-query';
import { useFormik } from 'formik';
import moment from 'moment';
// import { usePathname } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';
import { useRecoilState } from 'recoil';
import * as Yup from 'yup';

interface BookingHookProps {
  bookingOnClose: () => void;
  refetch?: () => void;
  data?: any; // Specific client data passed as props
}

export const useAddBookingHook = ({
  bookingOnClose,
  refetch,
  data,
}: BookingHookProps) => {
  const [filter] = useRecoilState(AllClientsFilterState);
  const [newClient, setNewClient] = useState(false);
  const [isExistingClient, setIsExistingClient] = useState('yes');
  const [loading, setLoading] = useState(false);
  const [searchResult, setSearchResult] = useState<Array<any>>([]);
  const [showSearch, setShowSearch] = useState(true);
  const { refetch: refetchAllClient } = useGetAllClientsQuery(filter);
  const queryClient = useQueryClient();

  // const path = usePathname();
  // const slp_id = path.split('/').filter(Boolean)[1];

  const { UserFromQuery } = useSupabaseSession();
  const raw = localStorage.getItem('UserState');
  const dataOrg = raw ? JSON.parse(raw) : null;
  const org = dataOrg?.UserState?.organization;

  const searchParams = new URLSearchParams(window.location.search);
  const orgIdFromUrl = searchParams.get('organization_id');

  // const { data: slpData, isLoading: slpLoading } = useGetAllUsersQuery();
  const { data: slpData, isLoading: slpLoading } = useGetAllSlpQuery({
    organization_id: orgIdFromUrl ? Number(orgIdFromUrl) : org?.id,
  });

  const clientOptions = [
    { label: 'Yes', value: 'yes' },
    { label: 'No', value: 'no' },
  ];

  const handleSelectClient = (item: any) => {
    setFieldValue('first_name', item.first_name);
    setFieldValue('last_name', item.last_name);
    setFieldValue(
      'display_name',
      item.display_name || `${item.first_name} ${item.last_name}` || ''
    );
    setFieldValue('email', item.initial_email || item.email);
    setFieldValue('client_id', item.id);
    setFieldValue('province', item.province);
    setFieldValue('phone', item.phone);
    setShowSearch(false);
  };

  const SlpOptions = useMemo(() => {
    return slpData
      ? slpData
          .filter((user: any) => user.status === 'Active')
          .map((item: any) => ({
            label: `${item?.first_name} ${item?.last_name}`,
            value: item?.email,
            id: item?.id,
          }))
      : [];
  }, [slpData]);
  // console.log('slp_id  is ', slp_id);
  // console.log('UserFromQuery  is ', UserFromQuery);

  const { data: ServicesData } = useGetServicesQuery(
    dataOrg?.UserState?.organization_id || UserFromQuery?.organization_id,
    {
      enabled:
        Boolean(dataOrg?.UserState?.organization_id) ||
        Boolean(UserFromQuery?.organization_id),
    }
  );

  // console.log('ServicesData', ServicesData);
  const servicesOption = useMemo(
    () =>
      ServicesData?.services?.map((item: any) => ({
        label: item?.name,
        value: item,
      })) || [],
    [ServicesData]
  );

  // console.log('productOptions', AllProducts);

  // console.log('isExistingClient', isExistingClient);

  // const eventOptions = [
  //   '15 Minute Free Consultation',
  //   '1 hour Assessment',
  //   '30 minute Session',
  //   '1 hour Session',
  //   '1 hour Session (package)',
  //   '30 minute Session (Package)',
  //   'BC - 1 hour Assessment',
  //   'ONTARIO - 1 Hour Assessment',
  //   '30 minute Training/Therapy Session',
  //   '30 minute Training/Therapy Session (package)',
  //   'Team check-in',
  //   'Speaking Skills Group',
  // ].map((item) => ({ label: item, value: item }));

  // const initialValues = {
  //   client_id: data?.id || '',
  //   first_name: data?.first_name || '',
  //   last_name: data?.last_name || '',
  //   display_name:
  //     data?.display_name || `${data?.first_name} ${data?.last_name}` || '',
  //   email: data?.email || '',
  //   phone: data?.phone || '',
  //   province: data?.province || '',
  //   appointment: moment().format('YYYY-MM-DDTHH:mm'),
  //   event: '15 Minute Free Consultation',
  //   assigned_to: '',
  // };

  // const validationSchema = Yup.object().shape({
  //   client_id: Yup.string().nullable(),
  //   first_name: Yup.string().required('Enter First name'),
  //   last_name: Yup.string().required('Enter Last name'),
  //   email: Yup.string().email().required('Enter a valid email'),
  //   province: Yup.string().required('Province is required'),

  //   phone: Yup.number()
  //     .typeError('Phone number must be a number')
  //     .required('Phone Number is required')
  //     .moreThan(0, 'Phone Number cannot be negative'),

  //   appointment: Yup.string().required(),
  //   event: Yup.string().required('Select Event'),
  //   assigned_to: Yup.string().required('Select Therapist'),
  // });

  const baseSchema = Yup.object({
    appointment: Yup.string().required(),
    event: Yup.string().required('Select Event'),
    assigned_to: Yup.string().required('Select Therapist'),
  });

  const getValidationSchema = (isExistingClient: string) =>
    baseSchema.shape({
      province:
        isExistingClient === 'yes' || !showSearch
          ? Yup.string().nullable()
          : Yup.string().required('Province is required'),
      client_id:
        isExistingClient === 'yes'
          ? Yup.string().required('Client ID is required')
          : Yup.string().nullable(),
      first_name:
        isExistingClient === 'yes'
          ? Yup.string().nullable()
          : Yup.string().required('Enter First name'),
      last_name:
        isExistingClient === 'yes'
          ? Yup.string().nullable()
          : Yup.string().required('Enter Last name'),
      email:
        isExistingClient === 'yes'
          ? Yup.string().nullable()
          : Yup.string().email().required('Enter a valid email'),
      phone:
        isExistingClient === 'yes' || !showSearch
          ? Yup.string().nullable()
          : Yup.string().matches(
              /^\+?[0-9 -]+$/,
              'Invalid phone number format'
            ),
    });

  const validationSchema = useMemo(
    () => getValidationSchema(isExistingClient),
    [isExistingClient, showSearch]
  );

  const {
    values,
    handleSubmit,
    errors,
    handleChange,
    submitCount,
    touched,
    setValues,
    resetForm,
    handleBlur,
    setFieldValue,
  } = useFormik({
    initialValues: {},
    validationSchema,
    enableReinitialize: true, // Allow values to update when `data` changes
    onSubmit: async (values: any) => {
      console.log('values', values);
      try {
        setLoading(true);

        let clientId = values.client_id;

        // if (isExistingClient === 'yes' && !clientId) {
        //   toaster.create({
        //     description: 'Please select a client',
        //     type: 'error',
        //   });
        //   return;
        // }

        // if (
        //   isExistingClient === 'no' &&
        //   (!values.first_name ||
        //     !values.last_name ||
        //     !values.email ||
        //     !values.phone)
        // ) {
        //   toaster.create({
        //     description: 'Please fill all the required fields',
        //     type: 'error',
        //   });
        //   return;
        // }

        if (!clientId) {
          const existingClient = await findExistingClient(values.email);

          if (existingClient?.length > 0) {
            clientId = existingClient[0].id;
          } else {
            const newClientData = {
              initial_email: values.email.toLowerCase(),
              first_name: values.first_name,
              last_name: values.last_name,
              display_name: `${values?.first_name} ${values?.last_name}`,
              phone: values.phone,
              province: values.province,
              lead_created: values.appointment,
              last_consultation_date: values.appointment,
              organization_id: orgIdFromUrl ? Number(orgIdFromUrl) : org?.id,
            };

            const newClient = await createClient(newClientData);
            if (newClient) {
              clientId = newClient[0].id;
              setNewClient(true);

              await createClientEmails(
                clientId,
                newClientData.initial_email.toLowerCase(),
                newClient[0]?.organization_id
              );
              // Log client creation in client_activities
              await supabase.from(tableNames.client_activities).insert({
                client_id: clientId,
                activity_type: 'client_created',
                organization_id: orgIdFromUrl ? Number(orgIdFromUrl) : org?.id,
                activity_date: new Date().toISOString(),
                details: {
                  created_by: 'booking',
                },
              });
            }
          }
        }

        const duplicate = await checkDuplicateBooking(
          {
            ...values,
            client_id: clientId,
          },
          orgIdFromUrl ? Number(orgIdFromUrl) : org?.id
        );
        let slpId = '';
        if (!duplicate.data || duplicate.data.length === 0) {
          slpId =
            SlpOptions?.length === 1
              ? SlpOptions?.[0]?.id
              : SlpOptions?.find(
                  (item: any) => item.value === values.assigned_to
                )?.id;
          // console.log('slp is ', slpId);

          const payload = {
            ...values,
            client_id: clientId,
            appointment: moment(values.appointment)
              .utc()
              .format('YYYY-MM-DD HH:mm:ss+00'),
            // Use consistent ISO-like format with timezone
            booking_created_at_raw: moment()
              .utc()
              .format('YYYY-MM-DD HH:mm:ss+00'),
            slp_id: slpId,
            calendly_event_type: 'invitee.created',
            organization_id: orgIdFromUrl ? Number(orgIdFromUrl) : org?.id,
          };

          // i had to delete the product_id to test it was causing error when creating a booking
          delete payload.product_id;

          console.log('payload', payload);
          delete payload.display_name;

          const { data: bookingData } = await supabase
            .from(tableNames.bookings)
            .insert(payload)
            .select()
            .single();

          console.log('bookingData----666', bookingData);
          await supabase.from(tableNames.client_activities).insert({
            client_id: clientId,
            activity_type: 'booking_created',
            organization_id: orgIdFromUrl ? Number(orgIdFromUrl) : org?.id,
            activity_date: new Date().toISOString(),
            details: {
              ...payload,
              booking_id: bookingData?.id,
              status: 'Pending',
            },
          });
        }

        await queryClient.invalidateQueries({
          queryKey: [queryKey.client.getById, Number(values.client_id)],
        });
        await queryClient.invalidateQueries({
          queryKey: [
            queryKey.bookings.getSlpBookings,
            {
              date: moment(values?.invoice_date).format('YYYY-MM-DD'),
              slpId: Number(slpId),
            },
          ],
        });

        refetch?.();
        refetchAllClient();
        toaster.create({
          description: ToastMessages.operationSuccess,
          type: 'success',
        });

        resetForm();
      } catch (error: any) {
        toaster.create({
          description: error.message || ToastMessages.somethingWrong,
          type: 'error',
        });
      } finally {
        setLoading(false);
        bookingOnClose();
      }
    },
  });

  const handleFormSubmit = (e: any) => {
    e.preventDefault();
    handleSubmit();
  };

  useEffect(() => {
    if (data && isExistingClient !== 'yes') {
      setIsExistingClient('yes');
    }

    if (isExistingClient && !data) {
      setShowSearch(true);
    }
    setValues({
      first_name: data?.first_name || '',
      last_name: data?.last_name || '',
      email: data?.initial_email || data?.email || '',
      display_name: data?.display_name
        ? `${data?.first_name} ${data?.last_name}`
        : '',
      phone: data?.phone || '',
      province: data?.province || '',
      client_id: data?.id || '',
      appointment: data?.appointment || moment().format('YYYY-MM-DDTHH:mm'),
      service_id: '',
      event: '',
      assigned_to: values?.assigned_to || '',
    });
  }, [data, isExistingClient]);

  useEffect(() => {
    if (SlpOptions.length === 1) {
      setFieldValue('assigned_to', SlpOptions[0].value);
    }
  }, [SlpOptions, setFieldValue]);

  return {
    values,
    handleFormSubmit,
    errors,
    handleChange,
    touched,
    submitCount,
    handleBlur,
    SlpOptions,
    slpLoading,
    setFieldValue,
    newClient,
    setNewClient,
    handleSelectClient,
    servicesOption,
    isExistingClient,
    showSearch,
    setIsExistingClient,
    resetForm,
    loading,
    clientOptions,
    searchResult,
    setSearchResult,
    setShowSearch,
  };
};
