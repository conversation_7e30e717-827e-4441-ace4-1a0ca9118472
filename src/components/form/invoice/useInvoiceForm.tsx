/* eslint-disable unused-imports/no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
// _hooks/useInvoiceForm.js
/* eslint-disable react-hooks/exhaustive-deps */

import { useGetClientByIdQuery } from '@/api/clients/get-client-by-id';
import {
  useCreateInvoiceMutation,
  useGetAllTaxesQuery,
  useUpdateInvoiceMutation,
} from '@/api/newsf/queries';
import { useGetPackageOfferingsQuery } from '@/api/package-offerings/get-package-offering-by-slp';
import { useGetServicesQuery } from '@/api/services/get-services-by-slp';
import { useGetUserByIdQuery } from '@/api/users/use-get-user-by-id';
import { toaster } from '@/components/ui/toaster';
import { queryKey } from '@/constants/query-key';
import { ToastMessages } from '@/constants/toast-messages';
import { useSupabaseSession } from '@/hooks/auth/useUserSession';
import { useQueryClient } from '@/lib/react-query';
import { Box, Text, useDisclosure } from '@chakra-ui/react';
import moment from 'moment';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { useCallback, useEffect, useMemo, useState } from 'react'; // Added useCallback

export const useInvoiceForm = (initialInvoiceData: any) => {
  // console.log('initialInvoiceData is ', initialInvoiceData);

  const { UserFromQuery } = useSupabaseSession();
  const searchParams = useSearchParams();

  const router = useRouter();
  const taxDisclosure = useDisclosure();
  const discountDisclosure = useDisclosure(); // Added discountDisclosure here
  const itemDisclosure = useDisclosure(); // Added itemDisclosure here

  const [targetItem, setTargetItem] = useState<any>({});

  const orgIdFromUrl = searchParams.get('organization_id');
  const userIdFromUrl = searchParams.get('user_id');
  const raw = localStorage.getItem('UserState');
  const dataOrg = raw ? JSON.parse(raw) : null;
  const org = dataOrg?.UserState?.organization;
  const currencyCode = org?.currency_code || 'CAD';

  const { data: Slp } = useGetUserByIdQuery(Number(userIdFromUrl), {
    enabled: Boolean(Number(userIdFromUrl)),
  });

  const queryClient = useQueryClient();
  const params = useParams();
  const { mutateAsync: createInvoiceApi, isLoading: createInvoiceLoading } =
    useCreateInvoiceMutation();

  const [filter, setFilter] = useState<any>({
    size: 50,
    currentPage: 1,
    organization_id:
      initialInvoiceData?.data?.organization_id ||
      orgIdFromUrl ||
      UserFromQuery?.organization_id,
    user_id:
      initialInvoiceData?.data?.slp_id || userIdFromUrl || UserFromQuery?.id,
  });

  const [invoiceDetails, setInvoiceDetails] = useState({
    memo: initialInvoiceData.memo || '',
    invoice_number: initialInvoiceData.invoice_number || '',
    invoiceDate: initialInvoiceData?.data?.invoice_date
      ? initialInvoiceData?.data?.invoice_date
      : new Date(),
    dueDate: initialInvoiceData.due_date
      ? new Date(initialInvoiceData.due_date)
      : new Date(),
    invoiceSubTotal: 0,
    total: 0,
    amountDue: 0,
    tax: 0,
    discount: 0,
    client_id: initialInvoiceData.client_id || null, // Initialize client_id
    name: '',
    currency_code: initialInvoiceData?.data?.currency_code || currencyCode, // Default to CAD
  });

  const [discountData, setDiscountData] = useState<{
    type: 'percentage' | 'currency';
    value: number;
  }>(initialInvoiceData.discount || { type: 'percentage', value: 0 });

  const [selectedItems, setSelectedItems] = useState<any[]>(
    initialInvoiceData.invoice_items?.map((item: any) => ({
      ...item,
      // Ensure itemId for consistency, if not already present
      itemId: item.id || Math.random().toString(36).substring(7),
      quantity: item.quantity,
      price: item.price,
      name: item.product_name || item.services?.name, // Use product_name or service name
      description: item.description || '', // Add description field
      taxes: item.taxes || [], // Ensure taxes array is present
      amount: item.quantity * item.price, // Calculate initial amount
    })) || []
  );

  const { data: TaxData } = useGetAllTaxesQuery(
    {
      user_id: filter.user_id || '',
      org_id: filter.organization_id,
    },
    {
      enabled: !!filter.organization_id,
      generateItemId: true,
    }
  );

  const { data: PackageOfferings } = useGetPackageOfferingsQuery(
    {
      user_id: filter.user_id || '',
      organization_id: filter.organization_id,
    },
    {
      enabled: !!filter.user_id,
      generateItemId: true,
    }
  );

  const { data: ServicesData } = useGetServicesQuery(filter.organization_id, {
    enabled: !!filter.organization_id,
    generateItemId: true,
  });

  const { data: Client } = useGetClientByIdQuery(
    Number(params?.client_id || invoiceDetails.client_id), // Use client_id from initial data if available
    {
      enabled: Boolean(Number(params?.client_id || invoiceDetails.client_id)),
    }
  );

  const { mutateAsync: UpdateInvoice, isLoading: UpdateInvoicePending } =
    useUpdateInvoiceMutation();

  const invoiceItemOptions = useMemo(() => {
    const serviceItemOptions =
      ServicesData?.services?.map((item: any) => ({
        label: (
          <Box>
            <Box display={'flex'} justifyContent={'space-between'}>
              <Text fontWeight={'bold'}> Service - {item?.name}</Text>
              <Text> ${item?.price.toFixed(2)}</Text>
            </Box>
            <Box>{item?.description}</Box>
          </Box>
        ),
        value: {
          ...item,
          type: 'service',
          itemId: Math.random().toString(36).substring(7), // Generate unique ID for new items
        },
      })) || [];

    const packageItemOptions =
      PackageOfferings?.map((item: any) => ({
        label: (
          <Box>
            <Box display={'flex'} justifyContent={'space-between'}>
              <Text fontWeight={'bold'}> Package - {item?.name}</Text>
              <Text> ${item?.price.toFixed(2)}</Text>
            </Box>
            <Box>{item?.description}</Box>
          </Box>
        ),
        value: {
          ...item,
          type: 'package',
          itemId: Math.random().toString(36).substring(7), // Generate unique ID for new items
        },
      })) || [];
    return [...serviceItemOptions, ...packageItemOptions];
  }, [ServicesData, PackageOfferings]);

  const taxOptions = useMemo(() => {
    return (
      TaxData?.data?.map((tax: any) => ({
        label: `${tax.name} (${tax.value}%)`,
        value: tax,
      })) || []
    );
  }, [TaxData]);

  const calculateItemTax = useCallback((item: any) => {
    if (!Array.isArray(item.taxes) || item.taxes.length === 0) return 0;

    const subtotal = Number(item.price) * Number(item.quantity ?? 1);

    const totalTaxPercentage = item.taxes.reduce((sum: number, tax: any) => {
      const value = Number(tax?.value);
      return !isNaN(value) ? sum + value : sum;
    }, 0);

    return (totalTaxPercentage / 100) * subtotal;
  }, []);

  const updateFigures = useCallback(
    (newItems: any[]) => {
      const subTotal = newItems?.reduce((prev, item) => {
        const quantity = Number(item?.quantity);
        const price = Number(item?.price);
        return prev + (isNaN(quantity) || isNaN(price) ? 0 : quantity * price);
      }, 0);

      const totalTax = newItems?.reduce((prev, item) => {
        return prev + calculateItemTax(item);
      }, 0);

      let discountAmount = 0;
      if (discountData.value > 0) {
        if (discountData.type === 'percentage') {
          discountAmount = (subTotal * discountData.value) / 100;
        } else {
          discountAmount = discountData.value;
        }
      }

      const total = subTotal + totalTax - discountAmount;
      const amountDue = total;

      setInvoiceDetails((prev) => ({
        ...prev,
        invoiceSubTotal: subTotal,
        total: total,
        amountDue: amountDue,
        tax: totalTax,
        discount: discountAmount,
      }));
    },
    [discountData, calculateItemTax]
  );

  const handleItemClick = (item: any) => {
    const itemFound = selectedItems?.find(
      (existingItem: any) => existingItem?.itemId === item?.itemId
    );

    if (itemFound) {
      const newItems = selectedItems?.map((existingItem: any) => {
        if (existingItem?.itemId === item?.itemId) {
          const quantity = (Number(itemFound?.quantity) || 0) + 1;
          return {
            ...itemFound,
            quantity,
            amount: (Number(itemFound?.price) || 0) * quantity,
          };
        }
        return existingItem;
      });
      setSelectedItems(newItems); // updateFigures will be called by useEffect
    } else {
      const { id, ...rest } = item;

      const newItems = [
        ...selectedItems,
        {
          ...rest,
          quantity: 1,
          amount: Number(item?.price) || 0,
          ...(item?.type === 'service'
            ? { service_id: item?.id }
            : { package_id: item?.id }),
          description: item.description || '', // Ensure description for new items
        },
      ];
      setSelectedItems(newItems); // updateFigures will be called by useEffect
    }
  };

  const updateItem = (field: string, value: any, itemId: any) => {
    const newItems = selectedItems?.map((item: any) => {
      if (item?.itemId === itemId) {
        const updatedItem = {
          ...item,
          [field]: value,
        };
        // Recalculate amount if quantity or price changes
        if (field === 'quantity' || field === 'price') {
          const quantity = Number(updatedItem?.quantity) || 0;
          const price = Number(updatedItem?.price) || 0;
          updatedItem.amount = quantity * price;
        }
        return updatedItem;
      }
      return item;
    });
    setSelectedItems(newItems); // updateFigures will be called by useEffect
  };

  const deleteItem = (itemId: any) => {
    const newItems = selectedItems?.filter((item) => item?.itemId !== itemId);
    setSelectedItems(newItems); // updateFigures will be called by useEffect
  };

  const handleApplyDiscount = (discount: {
    type: 'percentage' | 'currency';
    value: number;
  }) => {
    setDiscountData(discount);
    discountDisclosure.onClose();
  };

  const handleCreateInvoice = async () => {
    try {
      if (!invoiceDetails.client_id) {
        throw new Error('Please select a client.');
      }
      if (selectedItems.length === 0) {
        throw new Error('Please add at least one invoice item.');
      }

      const invoiceItemsPayload = selectedItems.map((item) => ({
        product_name: item.name,
        quantity: Number(item.quantity),
        price: Number(item.price),
        service_id: item.service_id,
        package_id: item.package_id,
        description: item.description,
        new_item: item.new_item,
        duration_minutes: item?.new_item ? item?.duration_minutes : undefined,
        tax_ids: item.taxes?.map((tax: any) => tax.id) || [],
      }));

      const payload = {
        currency_code: invoiceDetails.currency_code || currencyCode,
        client_id: invoiceDetails.client_id,
        organization_id: Number(orgIdFromUrl) || UserFromQuery?.organization_id,
        user_id: Number(userIdFromUrl) || UserFromQuery?.id,

        name: invoiceDetails.name,
        // invoice_date: moment(invoiceDetails.invoiceDate).format('YYYY-MM-DD'),
        invoice_date: moment(invoiceDetails?.invoiceDate)
          .utc()
          .format('YYYY-MM-DDTHH:mm:ss[Z]'),
        due_date: moment(invoiceDetails.dueDate).format('YYYY-MM-DD'),
        invoice_number: invoiceDetails.invoice_number,
        memo: invoiceDetails.memo,
        total_price: invoiceDetails.total,
        tax_value: invoiceDetails.tax,
        discount: {
          type: discountData.type,
          value: discountData.value,
        },
        slp: `${Slp?.first_name || UserFromQuery?.first_name} ${Slp?.last_name || UserFromQuery?.last_name}`,

        items: invoiceItemsPayload,
        // Add other necessary fields from initialInvoiceData if editing
        // ...(initialInvoiceData.id && { id: initialInvoiceData.id }),
      };
      console.log('Invoice Payload:', payload);
      // return;
      await createInvoiceApi(payload);
      toaster.create({
        description: initialInvoiceData.id
          ? 'Invoice updated successfully!'
          : 'Invoice created successfully!',
        type: 'success',
      });
      queryClient.invalidateQueries({
        queryKey: ['newsf-get-all-invoices'],
      }); // Invalidate invoices query
      queryClient.invalidateQueries({
        queryKey: [queryKey.newSf.getAllPurchases],
      }); // Invalidate invoices query

      const newUrl = `/contacts/${Client?.id}?tab=invoices`;
      if (orgIdFromUrl) {
        router.push(`${newUrl}&organization_id=${orgIdFromUrl}`);
      } else {
        router.push(newUrl);
      }
    } catch (error: any) {
      toaster.create({
        description: error?.message || ToastMessages.somethingWrong,
        type: 'error',
      });
    }
  };
  const handleEdit = async () => {
    try {
      if (!selectedItems || !initialInvoiceData?.data?.invoice_items) return;
      const originalItems = initialInvoiceData.data.invoice_items;

      const existingItemIds = selectedItems
        .filter((item) => item.isExisting)
        .map((item) => item.id);
      // console.log('existingItemIds is ', existingItemIds);
      // console.log('originalItems is ', originalItems);

      const deletedItems = originalItems
        .filter((item: any) => !existingItemIds.includes(item.id))
        .map((item: any) => ({ ...item, is_deleted: true }));

      const newItems = selectedItems
        .filter((item) => !item.id)
        .map((item) => ({
          ...item,
          product_name: item?.description,
          tax_ids: item?.taxes?.map((item: any) => Number(item?.id)),
        }));

      const updatedItems = selectedItems.filter(
        (item) => item.id && item.isExisting
      );

      // console.log('deletedItems is ', deletedItems);
      // console.log('newItems is ', newItems);
      // console.log('updatedItems is ', updatedItems);

      // return;
      await UpdateInvoice({
        id: initialInvoiceData?.data?.id,
        payload: {
          discount: {
            type: discountData.type,
            value: discountData.value,
          },
          tax_value: invoiceDetails?.tax,
          invoice_number: invoiceDetails?.invoice_number,
          memo: invoiceDetails?.memo,
          invoice_date: moment(invoiceDetails.invoiceDate).format('YYYY-MM-DD'),
          due_date: moment(invoiceDetails.dueDate).format('YYYY-MM-DD'),
          currency_code: invoiceDetails.currency_code || currencyCode,
          newItems,
          deletedItems,
          updatedItems,
        },
      });
      toaster.create({
        description: 'Invoice updated successfully!',
        type: 'success',
      });
      router.back();
      queryClient.invalidateQueries({
        queryKey: ['newsf-get-invoice-by-id', initialInvoiceData?.data?.id],
      });
      queryClient.invalidateQueries({
        queryKey: [queryKey.newSf.getAllPurchases],
      });
    } catch (error: any) {
      toaster.create({
        description: error?.message || ToastMessages.somethingWrong,
        type: 'error',
      });
    }
  };
  const handleInvoiceDetailsChange = (field: string, value: any) => {
    setInvoiceDetails((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Actions for CustomSelect 'Add new tax'
  const actions = {
    openTaxModal: (item: any) => {
      setTargetItem(item);
      taxDisclosure.onOpen();
    },
  };

  // Effects
  useEffect(() => {
    if (!Client) return;
    setInvoiceDetails((prev) => ({
      ...prev,
      client_id: Client?.id,
      name: Client?.display_name || '',
    }));
  }, [Client]);

  useEffect(() => {
    if (initialInvoiceData?.data) return;
    setFilter((prev: any) => ({
      ...prev,
      organization_id: Number(orgIdFromUrl) || UserFromQuery?.organization_id,
      user_id: Number(userIdFromUrl) || UserFromQuery?.id,
    }));
  }, [orgIdFromUrl, UserFromQuery, userIdFromUrl]);

  // Recalculate figures when selectedItems or discountData changes
  useEffect(() => {
    updateFigures(selectedItems);
  }, [selectedItems, discountData]);

  // Initialize form with initialInvoiceData on component mount/initialData change
  useEffect(() => {
    if (initialInvoiceData && Object.keys(initialInvoiceData).length > 0) {
      setInvoiceDetails((prev) => ({
        ...prev,
        memo: initialInvoiceData?.data?.memo || '',
        invoice_number: initialInvoiceData?.data?.invoice_number || '',
        invoiceDate: initialInvoiceData?.data?.invoice_date
          ? initialInvoiceData?.data?.invoice_date
          : new Date(),
        dueDate: initialInvoiceData?.data?.due_date
          ? new Date(initialInvoiceData?.data?.due_date)
          : new Date(),
        client_id: initialInvoiceData?.data?.client_id || null,
        currency_code: initialInvoiceData?.data?.currency_code || currencyCode,
      }));
      setDiscountData(
        initialInvoiceData?.data?.discount || { type: 'percentage', value: 0 }
      );
      setSelectedItems(
        initialInvoiceData?.data?.invoice_items?.map((item: any) => ({
          ...item,
          itemId: item?.id,
          quantity: item?.quantity,
          amount: item?.quantity * item?.price,

          price: item?.price,
          name: item?.services?.name || item?.product_name,
          description: item?.services?.description || item?.description || '',
          taxes: item?.taxes || [],
          isExisting: true,
          ...(item?.service_id && { type: 'service' }),
          ...(item?.package_id && { type: 'package' }),
        })) || []
      );
    }
  }, [initialInvoiceData]);

  return {
    itemDisclosure,
    deleteItem,
    updateItem,
    Client,
    handleCreateInvoice,
    createInvoiceLoading,
    selectedItems,
    calculateItemTax,
    discountData,
    handleApplyDiscount,
    discountDisclosure,
    actions,
    taxOptions,
    targetItem,
    taxDisclosure,
    invoiceItemOptions,
    invoiceDetails,
    handleInvoiceDetailsChange,
    handleItemClick,
    handleEdit,
    UpdateInvoicePending,
  };
};
